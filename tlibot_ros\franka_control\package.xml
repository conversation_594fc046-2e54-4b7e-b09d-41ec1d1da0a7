<?xml version="1.0"?>
<package format="2">
  <name>franka_control</name>
  <version>0.10.1</version>
  <description>franka_control provides a hardware node to control a Franka Emika research robot</description>
  <maintainer email="<EMAIL>">Franka Emika GmbH</maintainer>
  <license>Apache 2.0</license>

  <url type="website">http://wiki.ros.org/franka_control</url>
  <url type="repository">https://github.com/frankaemika/franka_ros</url>
  <url type="bugtracker">https://github.com/frankaemika/franka_ros/issues</url>
  <author>Franka Emika GmbH</author>

  <buildtool_depend>catkin</buildtool_depend>

  <depend>libfranka</depend>
  <depend>controller_interface</depend>
  <depend>controller_manager</depend>
  <depend>franka_hw</depend>
  <depend>franka_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>pluginlib</depend>
  <depend>realtime_tools</depend>
  <depend>roscpp</depend>
  <depend>sensor_msgs</depend>
  <depend>tf2_msgs</depend>
  <depend>tf</depend>
  <depend>std_srvs</depend>

  <exec_depend>franka_description</exec_depend>
  <exec_depend>franka_gripper</exec_depend>
  <exec_depend>joint_state_publisher</exec_depend>
  <exec_depend>joint_trajectory_controller</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>

  <export>
    <controller_interface plugin="${prefix}/franka_controller_plugins.xml"/>
  </export>
</package>
