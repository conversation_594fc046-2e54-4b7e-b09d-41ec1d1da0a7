#pragma once

#include <string>
#include <cstdint>

namespace tcb710_sdk {
namespace constants {

// 数据帧头
constexpr uint16_t FRAME_HEADER = 0x4e66;

// 6001端口命令字
namespace port_6001 {
    // 故障处理
    constexpr uint16_t FAULT_RESET = 0x3201;
    
    // 控制器管理
    constexpr uint16_t REBOOT_CONTROLLER = 0x5061;
    constexpr uint16_t CONTROLLER_INIT_FINISH = 0x4305;
    
    // 控制器IP设置
    namespace controller_ip {
        constexpr uint16_t SET = 0x4301;
        constexpr uint16_t INQUIRE = 0x4302;
    }
    
    // 通讯周期控制
    namespace control_cycle {
        constexpr uint16_t SET = 0x2E07;
        constexpr uint16_t INQUIRE = 0x2E08;
    }
    
    // 伺服上下电状态
    namespace deadman_status {
        constexpr uint16_t SET = 0x2301;
        constexpr uint16_t INQUIRE = 0x2302;
    }
    
    // 伺服命令
    namespace servo_commands {
        constexpr uint16_t SET = 0x2001;
        constexpr uint16_t INQUIRE = 0x2002;
    }
    
    constexpr uint16_t SERVO_CONNECT_INQUIRE = 0x5042;
    
    // 伺服内部参数
    namespace servo_inside_parm {
        constexpr uint16_t INQUIRE = 0x5072;
        constexpr uint16_t SET = 0x5071;
    }
    
    constexpr uint16_t SLAVETYPE_LIST_INQUIRE = 0x2e0e;
    
    // 操作模式
    namespace operation_mode {
        constexpr uint16_t SET = 0x2101;
        constexpr uint16_t INQUIRE = 0x2102;
    }
    
    // 关节参数
    namespace joint_parameter {
        constexpr uint16_t SET = 0x3B01;
        constexpr uint16_t INQUIRE = 0x3B02;
    }
    
    // 直接运动控制
    namespace direct_motion {
        constexpr uint16_t MODE_SET = 0x50B1;
        constexpr uint16_t INSERT_INSTRVEC = 0x50B4;
        constexpr uint16_t MODE_SUSPEND = 0x50B7;
        constexpr uint16_t MODE_START = 0x50B8;
        constexpr uint16_t MODE_STOP = 0x50B9;
        constexpr uint16_t KEEP_POWER_ON = 0x50ba;
    }
    
    // 作业控制
    namespace job_control {
        constexpr uint16_t JOBSEND_DONE = 0x2501;
        constexpr uint16_t STOP_JOB_RUN = 0x2503;
    }
    
    constexpr uint16_t JOBFILE_LIST_INQUIRE = 0x5532;
    
    // 速度控制
    namespace speed_control {
        constexpr uint16_t SET = 0x2601;
        constexpr uint16_t INQUIRE = 0x2602;
    }
    
    constexpr uint16_t CURRENTPOS_INQUIRE = 0x2a02;
    
    // 机器人运动
    namespace robot_movement {
        constexpr uint16_t JOINT = 0x4501;   // MOVJ
        constexpr uint16_t LINEAR = 0x4502;  // MOVL
        constexpr uint16_t CIRCULAR = 0x4503; // MOVC
        constexpr uint16_t SPLINE = 0x4504;  // MOVS
    }
    
    // 点动操作
    namespace jog_operation {
        constexpr uint16_t MOVE = 0x2901;
        constexpr uint16_t STOP = 0x2902;
    }

    // 点动关节参数
    namespace jog_joint_parameter {
        constexpr uint16_t SET = 0x2604;
        constexpr uint16_t INQUIRE = 0x2605;
    }

    // 点动灵敏度
    namespace jog_sensitivity {
        constexpr uint16_t SET = 0x260A;
        constexpr uint16_t INQUIRE = 0x260B;
    }
    
    // 直角坐标点动参数
    namespace jog_rect_parameter {
        constexpr uint16_t SET = 0x2607;
        constexpr uint16_t INQUIRE = 0x2608;
    }
    
    // 坐标模式
    namespace coord_mode {
        constexpr uint16_t SET = 0x2201;
        constexpr uint16_t INQUIRE = 0x2202;
    }
    
    // 运动参数
    namespace interpolation_mode {
        constexpr uint16_t SET = 0x2801;
        constexpr uint16_t INQUIRE = 0x2802;
    }
    
    // 笛卡尔坐标参数
    namespace decare_parameter {
        constexpr uint16_t SET = 0x3B04;
        constexpr uint16_t INQUIRE = 0x3B05;
    }
    
    // 电机速度查询
    constexpr uint16_t CURRENTVEL_INQUIRE = 0x2A04;
    
    // 电机扭矩查询
    constexpr uint16_t CURRENTTORQ_INQUIRE = 0x2A06;
    
    // 轴速度查询
    constexpr uint16_t AXISACTUALVEL_INQUIRE = 0x2A22;
    
    // 伺服控制
    namespace servo_control {
        constexpr uint16_t OPEN = 0x95a1;
        constexpr uint16_t MOVE = 0x95a4;
    }
}

// 7000端口命令字
namespace port_7000 {
    constexpr uint16_t MULTI_POINT = 0x9521;
    
}

// 默认配置值
namespace defaults {
    constexpr const char* DEFAULT_IP = "*************";
    constexpr int DEFAULT_PORT_6001 = 6001;
    constexpr int DEFAULT_PORT_7000 = 7000;
    constexpr int DEFAULT_TIMEOUT_MS = 5000;
    constexpr int DEFAULT_RETRY_COUNT = 3;
    constexpr double DEFAULT_SPEED = 100.0;
    constexpr double DEFAULT_ACCELERATION = 100.0;
    constexpr double DEFAULT_DECELERATION = 100.0;
    constexpr int DEFAULT_PL = 0;
    constexpr size_t JOINT_COUNT = 7;
    constexpr double POSITION_TOLERANCE = 0.1;
    constexpr int MAX_WAIT_TIMEOUT = 60;
}

// 辅助函数：将命令字转换为字节数组
inline std::string commandToHex(uint16_t command) {
    char buffer[5];
    snprintf(buffer, sizeof(buffer), "%04X", command);
    return std::string(buffer);
}

// 辅助函数：将帧头转换为字节数组
inline std::string frameHeaderToHex() {
    return commandToHex(FRAME_HEADER);
}

} // namespace constants
} // namespace tcb710_sdk
