// Test file for FDR2 joint limits interface
#include <gtest/gtest.h>
#include <array>
#include <string>
#include <iostream>

// Include our FDR2 limits
#include <franka_hw/fdr2_limits.h>

class FDR2JointLimitsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup test data
    }

    void TearDown() override {
        // Cleanup
    }
};

// Test FDR2 joint acceleration limits
TEST_F(FDR2JointLimitsTest, TestAccelerationLimits) {
    // Verify that all acceleration limits are positive and reasonable
    for (size_t i = 0; i < fdr2::kMaxJointAcceleration.size(); i++) {
        EXPECT_GT(fdr2::kMaxJointAcceleration[i], 0.0) 
            << "Joint " << i << " acceleration limit should be positive";
        EXPECT_LT(fdr2::kMaxJointAcceleration[i], 50.0) 
            << "Joint " << i << " acceleration limit seems too high";
    }
}

// Test FDR2 joint jerk limits
TEST_F(FDR2JointLimitsTest, TestJerkLimits) {
    // Verify that all jerk limits are positive and reasonable
    for (size_t i = 0; i < fdr2::kMaxJointJerk.size(); i++) {
        EXPECT_GT(fdr2::kMaxJointJerk[i], 0.0) 
            << "Joint " << i << " jerk limit should be positive";
        EXPECT_LT(fdr2::kMaxJointJerk[i], 20000.0) 
            << "Joint " << i << " jerk limit seems too high";
    }
}

// Test FDR2 joint names
TEST_F(FDR2JointLimitsTest, TestJointNames) {
    // Verify joint names are correct
    for (size_t i = 0; i < fdr2::kJointNames.size(); i++) {
        std::string expected_name = "joint" + std::to_string(i + 1);
        EXPECT_EQ(std::string(fdr2::kJointNames[i]), expected_name)
            << "Joint " << i << " name should be " << expected_name;
    }
}

// Test array sizes
TEST_F(FDR2JointLimitsTest, TestArraySizes) {
    EXPECT_EQ(fdr2::kMaxJointAcceleration.size(), 7u) 
        << "Should have 7 acceleration limits";
    EXPECT_EQ(fdr2::kMaxJointJerk.size(), 7u) 
        << "Should have 7 jerk limits";
    EXPECT_EQ(fdr2::kJointNames.size(), 7u) 
        << "Should have 7 joint names";
}

// Mock test for robot type detection
class MockFrankaHW {
private:
    std::array<std::string, 7> joint_names_;
    std::string arm_id_;

public:
    MockFrankaHW(const std::array<std::string, 7>& joint_names, const std::string& arm_id)
        : joint_names_(joint_names), arm_id_(arm_id) {}

    bool isFdr2Robot() const {
        // Check if arm_id contains "fdr2" or if joint names match FDR2 pattern
        if (arm_id_.find("fdr2") != std::string::npos || arm_id_.find("FDR2") != std::string::npos) {
            return true;
        }
        
        // Check if all joint names match the FDR2 pattern (joint1, joint2, ..., joint7)
        bool matches_fdr2_pattern = true;
        for (size_t i = 0; i < joint_names_.size(); i++) {
            std::string expected_name = "joint" + std::to_string(i + 1);
            if (joint_names_[i] != expected_name) {
                matches_fdr2_pattern = false;
                break;
            }
        }
        
        return matches_fdr2_pattern;
    }
};

// Test robot type detection
TEST_F(FDR2JointLimitsTest, TestRobotTypeDetection) {
    // Test FDR2 robot detection by arm_id
    std::array<std::string, 7> any_joints = {"j1", "j2", "j3", "j4", "j5", "j6", "j7"};
    MockFrankaHW fdr2_by_id(any_joints, "fdr2_arm");
    EXPECT_TRUE(fdr2_by_id.isFdr2Robot()) << "Should detect FDR2 by arm_id";

    MockFrankaHW fdr2_by_id_upper(any_joints, "FDR2_arm");
    EXPECT_TRUE(fdr2_by_id_upper.isFdr2Robot()) << "Should detect FDR2 by arm_id (uppercase)";

    // Test FDR2 robot detection by joint names
    std::array<std::string, 7> fdr2_joints = {"joint1", "joint2", "joint3", "joint4", "joint5", "joint6", "joint7"};
    MockFrankaHW fdr2_by_joints(fdr2_joints, "some_arm");
    EXPECT_TRUE(fdr2_by_joints.isFdr2Robot()) << "Should detect FDR2 by joint names";

    // Test non-FDR2 robot
    std::array<std::string, 7> franka_joints = {"panda_joint1", "panda_joint2", "panda_joint3", "panda_joint4", "panda_joint5", "panda_joint6", "panda_joint7"};
    MockFrankaHW franka_robot(franka_joints, "panda_arm");
    EXPECT_FALSE(franka_robot.isFdr2Robot()) << "Should not detect Franka as FDR2";
}

int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
