#!/usr/bin/env python3
"""
验证CartesianStateInterface实现的脚本
检查接口是否正确注册和工作
"""

import rospy
import sys
from geometry_msgs.msg import PoseStamped, TwistStamped, AccelStamped
from std_msgs.msg import Header
import time

class CartesianStateVerifier:
    def __init__(self):
        rospy.init_node('cartesian_state_verifier', anonymous=True)
        
        # 数据存储
        self.pose_data = None
        self.velocity_data = None
        self.acceleration_data = None
        
        # 订阅测试控制器发布的话题
        self.pose_sub = rospy.Subscriber('/cartesian_pose', PoseStamped, self.pose_callback)
        self.velocity_sub = rospy.Subscriber('/cartesian_velocity', TwistStamped, self.velocity_callback)
        self.acceleration_sub = rospy.Subscriber('/cartesian_acceleration', AccelStamped, self.acceleration_callback)
        
        rospy.loginfo("CartesianStateVerifier: 初始化完成")
        
    def pose_callback(self, msg):
        self.pose_data = msg
        rospy.logdebug(f"收到位姿数据: x={msg.pose.position.x:.3f}, y={msg.pose.position.y:.3f}, z={msg.pose.position.z:.3f}")
        
    def velocity_callback(self, msg):
        self.velocity_data = msg
        rospy.logdebug(f"收到速度数据: vx={msg.twist.linear.x:.3f}, vy={msg.twist.linear.y:.3f}, vz={msg.twist.linear.z:.3f}")
        
    def acceleration_callback(self, msg):
        self.acceleration_data = msg
        rospy.logdebug(f"收到加速度数据: ax={msg.accel.linear.x:.3f}, ay={msg.accel.linear.y:.3f}, az={msg.accel.linear.z:.3f}")
    
    def verify_data_reception(self, timeout=10.0):
        """验证是否能接收到所有类型的数据"""
        rospy.loginfo("开始验证数据接收...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.pose_data and self.velocity_data and self.acceleration_data:
                rospy.loginfo("✅ 成功接收到所有类型的笛卡尔状态数据")
                return True
            rospy.sleep(0.1)
        
        # 检查哪些数据缺失
        missing = []
        if not self.pose_data:
            missing.append("位姿")
        if not self.velocity_data:
            missing.append("速度")
        if not self.acceleration_data:
            missing.append("加速度")
            
        rospy.logerr(f"❌ 超时未收到数据: {', '.join(missing)}")
        return False
    
    def verify_data_validity(self):
        """验证数据的有效性"""
        rospy.loginfo("开始验证数据有效性...")
        
        if not all([self.pose_data, self.velocity_data, self.acceleration_data]):
            rospy.logerr("❌ 数据不完整，无法验证有效性")
            return False
        
        # 验证位姿数据
        pose = self.pose_data.pose
        if abs(pose.position.x) > 10 or abs(pose.position.y) > 10 or abs(pose.position.z) > 10:
            rospy.logwarn("⚠️  位姿数据可能异常，位置值过大")
        
        # 验证四元数归一化
        q = pose.orientation
        quat_norm = (q.x**2 + q.y**2 + q.z**2 + q.w**2)**0.5
        if abs(quat_norm - 1.0) > 0.01:
            rospy.logwarn(f"⚠️  四元数未归一化: norm={quat_norm:.3f}")
        
        # 验证速度数据合理性
        vel = self.velocity_data.twist
        max_linear_vel = max(abs(vel.linear.x), abs(vel.linear.y), abs(vel.linear.z))
        max_angular_vel = max(abs(vel.angular.x), abs(vel.angular.y), abs(vel.angular.z))
        
        if max_linear_vel > 5.0:  # 5 m/s
            rospy.logwarn(f"⚠️  线速度可能过大: {max_linear_vel:.3f} m/s")
        if max_angular_vel > 10.0:  # 10 rad/s
            rospy.logwarn(f"⚠️  角速度可能过大: {max_angular_vel:.3f} rad/s")
        
        rospy.loginfo("✅ 数据有效性验证完成")
        return True
    
    def print_current_state(self):
        """打印当前状态信息"""
        if not all([self.pose_data, self.velocity_data, self.acceleration_data]):
            rospy.logwarn("数据不完整，无法显示完整状态")
            return
        
        print("\n" + "="*50)
        print("当前笛卡尔状态:")
        print("="*50)
        
        # 位姿信息
        pose = self.pose_data.pose
        print(f"位置: x={pose.position.x:.3f}m, y={pose.position.y:.3f}m, z={pose.position.z:.3f}m")
        print(f"姿态: x={pose.orientation.x:.3f}, y={pose.orientation.y:.3f}, z={pose.orientation.z:.3f}, w={pose.orientation.w:.3f}")
        
        # 速度信息
        vel = self.velocity_data.twist
        print(f"线速度: vx={vel.linear.x:.3f}m/s, vy={vel.linear.y:.3f}m/s, vz={vel.linear.z:.3f}m/s")
        print(f"角速度: wx={vel.angular.x:.3f}rad/s, wy={vel.angular.y:.3f}rad/s, wz={vel.angular.z:.3f}rad/s")
        
        # 加速度信息
        acc = self.acceleration_data.accel
        print(f"线加速度: ax={acc.linear.x:.3f}m/s², ay={acc.linear.y:.3f}m/s², az={acc.linear.z:.3f}m/s²")
        print(f"角加速度: αx={acc.angular.x:.3f}rad/s², αy={acc.angular.y:.3f}rad/s², αz={acc.angular.z:.3f}rad/s²")
        print("="*50)
    
    def run_verification(self):
        """运行完整的验证流程"""
        rospy.loginfo("开始CartesianStateInterface验证...")
        
        # 1. 验证数据接收
        if not self.verify_data_reception():
            return False
        
        # 2. 验证数据有效性
        if not self.verify_data_validity():
            return False
        
        # 3. 显示当前状态
        self.print_current_state()
        
        rospy.loginfo("✅ CartesianStateInterface验证完成")
        return True

def main():
    try:
        verifier = CartesianStateVerifier()
        
        # 等待一段时间让连接建立
        rospy.sleep(2.0)
        
        # 运行验证
        success = verifier.run_verification()
        
        if success:
            rospy.loginfo("🎉 CartesianStateInterface实现验证成功!")
            
            # 持续监控一段时间
            rospy.loginfo("持续监控10秒...")
            for i in range(10):
                rospy.sleep(1.0)
                if i % 3 == 0:  # 每3秒显示一次状态
                    verifier.print_current_state()
        else:
            rospy.logerr("❌ CartesianStateInterface实现验证失败!")
            return 1
            
    except rospy.ROSInterruptException:
        rospy.loginfo("验证被用户中断")
    except Exception as e:
        rospy.logerr(f"验证过程中发生错误: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
