<?xml version="1.0" ?>
<launch>
  <arg name="robot_ip" default="*************" />
  <arg name="load_gripper" default="true" />
  <arg name="arm_id" default="panda" />
  <arg name="publish_rate" default="10.0" />

  <!-- Load the URDF into the ROS Parameter Server -->
  <param name="robot_description" command="$(find xacro)/xacro $(find franka_description)/robots/panda_arm.urdf.xacro hand:=$(arg load_gripper)" />

  <!-- Start the hardware interface -->
  <include file="$(find franka_hw)/launch/franka_control.launch">
    <arg name="robot_ip" value="$(arg robot_ip)" />
    <arg name="load_gripper" value="$(arg load_gripper)" />
  </include>

  <!-- Load controller configuration -->
  <rosparam file="$(find franka_example_controllers)/config/franka_example_controllers.yaml" command="load" />

  <!-- Load and start the CartesianStateTestController -->
  <node name="controller_spawner" pkg="controller_manager" type="spawner" respawn="false" output="screen"
        args="cartesian_state_test_controller" />

  <!-- Controller configuration -->
  <rosparam param="cartesian_state_test_controller">
    type: franka_example_controllers/CartesianStateTestController
    arm_id: $(arg arm_id)
    publish_rate: $(arg publish_rate)
  </rosparam>

  <!-- Start RViz for visualization -->
  <node name="rviz" pkg="rviz" type="rviz" output="screen" args="-d $(find franka_example_controllers)/launch/rviz/franka_description_with_marker.rviz" />

  <!-- Optional: Echo topics for debugging -->
  <node name="echo_pose" pkg="rostopic" type="rostopic" args="echo /cartesian_pose" output="screen" if="false" />
  <node name="echo_velocity" pkg="rostopic" type="rostopic" args="echo /cartesian_velocity" output="screen" if="false" />
  <node name="echo_acceleration" pkg="rostopic" type="rostopic" args="echo /cartesian_acceleration" output="screen" if="false" />

</launch>
