<?xml version="1.0"?>
<package format="2">
  <name>franka_msgs</name>
  <version>0.10.1</version>
  <description>franka_msgs provides messages specific to Franka Emika research robots</description>
  <maintainer email="<EMAIL>">Franka Emika GmbH</maintainer>
  <license>Apache 2.0</license>

  <url type="website">http://wiki.ros.org/franka_msgs</url>
  <url type="repository">https://github.com/frankaemika/franka_ros</url>
  <url type="bugtracker">https://github.com/frankaemika/franka_ros/issues</url>
  <author>Franka Emika GmbH</author>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>message_generation</build_depend>

  <depend>std_msgs</depend>
  <depend>actionlib_msgs</depend>

  <exec_depend>message_runtime</exec_depend>

  <build_export_depend>message_runtime</build_export_depend>

  <export>
    <architecture_independent/>
  </export>
</package>
