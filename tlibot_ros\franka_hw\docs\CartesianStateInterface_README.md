# CartesianStateInterface 实现文档

## 概述

基于最新的robotarm_sdk，我们成功实现了CartesianStateInterface接口，使控制器能够访问机器人的笛卡尔状态信息，包括位姿(pose)、速度(velocity)和加速度(acceleration)。

## 实现特性

### ✅ 已实现功能

1. **CartesianStateInterface接口**
   - 提供笛卡尔位姿、速度、加速度的只读访问
   - 符合ros_control框架标准
   - 支持多控制器同时访问

2. **数据源集成**
   - 位姿数据：通过`robotarm_sdk.currentPosInquiry(CoordinateType::CART)`获取
   - 速度数据：通过`robotarm_sdk.axisActualVelInquire()`获取并计算
   - 加速度数据：通过数值微分计算

3. **坐标转换**
   - 自动处理mm到m的单位转换
   - 欧拉角到变换矩阵的转换
   - 4x4变换矩阵格式输出

## 代码结构

### 核心文件

1. **franka_hw/include/franka_hw/franka_cartesian_state_interface.h**
   - `CartesianStateHandle`: 笛卡尔状态句柄
   - `CartesianStateInterface`: 硬件接口类

2. **franka_hw/src/franka_hw.cpp**
   - `setupCartesianStateInterface()`: 接口设置函数
   - `calculateCartesianVelocities()`: 速度计算
   - `calculateCartesianAccelerations()`: 加速度计算

3. **测试控制器**
   - `cartesian_state_test_controller.h/cpp`: 测试控制器
   - 发布笛卡尔状态到ROS话题进行验证

## 使用方法

### 1. 在控制器中使用

```cpp
#include <franka_hw/franka_cartesian_state_interface.h>

class YourController : public controller_interface::MultiInterfaceController<
                           franka_hw::CartesianStateInterface> {
private:
    franka_hw::CartesianStateInterface* cartesian_state_interface_;
    std::unique_ptr<franka_hw::CartesianStateHandle> cartesian_state_handle_;

public:
    bool init(hardware_interface::RobotHW* robot_hardware, ros::NodeHandle& node_handle) override {
        // 获取接口
        cartesian_state_interface_ = robot_hardware->get<franka_hw::CartesianStateInterface>();
        
        // 获取句柄
        cartesian_state_handle_ = std::make_unique<franka_hw::CartesianStateHandle>(
            cartesian_state_interface_->getHandle(arm_id + "_cartesian_state"));
        
        return true;
    }
    
    void update(const ros::Time&, const ros::Duration&) override {
        // 读取笛卡尔状态
        const auto& pose = cartesian_state_handle_->getPose();           // 4x4变换矩阵
        const auto& velocity = cartesian_state_handle_->getVelocity();   // [vx,vy,vz,wx,wy,wz]
        const auto& acceleration = cartesian_state_handle_->getAcceleration(); // [ax,ay,az,αx,αy,αz]
        
        // 使用状态数据...
    }
};
```

### 2. 运行测试控制器

```bash
# 启动测试控制器
roslaunch franka_example_controllers cartesian_state_test_controller.launch robot_ip:=*************

# 查看发布的话题
rostopic echo /cartesian_pose
rostopic echo /cartesian_velocity  
rostopic echo /cartesian_acceleration
```

## 数据格式

### 位姿 (Pose)
- 类型: `std::array<double, 16>`
- 格式: 4x4变换矩阵，列主序存储
- 单位: 位置(m), 旋转(rad)

### 速度 (Velocity)
- 类型: `std::array<double, 6>`
- 格式: [vx, vy, vz, wx, wy, wz]
- 单位: 线速度(m/s), 角速度(rad/s)

### 加速度 (Acceleration)
- 类型: `std::array<double, 6>`
- 格式: [ax, ay, az, αx, αy, αz]
- 单位: 线加速度(m/s²), 角加速度(rad/s²)

## 技术细节

### 数据更新流程

1. **read()函数调用**
   - 从SDK读取关节和笛卡尔位置
   - 读取轴速度信息
   - 计算笛卡尔速度和加速度

2. **坐标转换**
   - SDK返回: [x(mm), y(mm), z(mm), yaw, pitch, roll, 额外轴]
   - 转换为: 4x4变换矩阵(位置单位m)

3. **速度计算**
   - 线速度: 从SDK的actualLineVel计算
   - 角速度: 通过雅可比矩阵计算(当前简化实现)

4. **加速度计算**
   - 通过数值微分计算: a = (v_current - v_previous) / dt

### 性能考虑

- 更新频率: 与硬件接口read()频率一致
- 内存开销: 最小，只存储必要的状态数据
- 计算开销: 轻量级，主要是数值微分

## 已知限制

1. **角速度计算**: 当前使用简化实现，需要完整的雅可比矩阵计算
2. **网络延迟**: SDK通信可能引入延迟，影响实时性
3. **数值微分**: 加速度计算对噪声敏感

## 后续改进

1. 实现完整的雅可比矩阵计算
2. 添加滤波器减少噪声
3. 优化网络通信性能
4. 添加更多状态验证

## 测试验证

使用提供的测试控制器可以验证：
- 接口是否正确注册
- 数据是否正确读取
- 坐标转换是否准确
- 实时性能是否满足要求
