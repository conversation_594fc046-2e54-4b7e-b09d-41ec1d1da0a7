// Copyright (c) 2024 FDR2 Robot
// Use of this source code is governed by the Apache-2.0 license, see LICENSE
#pragma once

#include <array>

namespace fdr2 {

/**
 * Maximum joint acceleration for FDR2 robot (rad/s²)
 * These values are conservative estimates for safe operation
 */
constexpr std::array<double, 7> kMaxJointAcceleration = {
    15.0,  // joint1
    7.5,   // joint2
    10.0,  // joint3
    12.5,  // joint4
    15.0,  // joint5
    20.0,  // joint6
    20.0   // joint7
};

/**
 * Maximum joint jerk for FDR2 robot (rad/s³)
 * These values are conservative estimates for safe operation
 */
constexpr std::array<double, 7> kMaxJointJerk = {
    7500.0,  // joint1
    3750.0,  // joint2
    5000.0,  // joint3
    6250.0,  // joint4
    7500.0,  // joint5
    10000.0, // joint6
    10000.0  // joint7
};

/**
 * Default joint names for FDR2 robot
 */
constexpr std::array<const char*, 7> kJointNames = {
    "joint1",
    "joint2", 
    "joint3",
    "joint4",
    "joint5",
    "joint6",
    "joint7"
};

}  // namespace fdr2
