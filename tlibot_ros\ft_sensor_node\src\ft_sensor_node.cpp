#include "ft_sensor_node.h"
#include <ros/console.h>
#include <geometry_msgs/WrenchStamped.h>
#include <realtime_tools/realtime_publisher.h>
#include <serial/serial.h>
#include <cstring>
#include <chrono>
#include <thread>
#include <iostream>
#include <iomanip>
#include <cstddef>
#include <sstream>

static const size_t MAX_READ_SIZE = 1024;

namespace ft_sensor {

FTSensorNode::FTSensorNode() 
    : nh_()
    , private_nh_("~")
    , running_(false) {
}

FTSensorNode::~FTSensorNode() {
    stop();
}

bool FTSensorNode::initialize() {
    // 读取配置参数
    if (!private_nh_.getParam("serial_port", serial_port_name_)) {
        ROS_ERROR("Failed to get serial_port parameter");
        return false;
    }
    
    if (!private_nh_.getParam("baud_rate", baud_rate_)) {
        ROS_ERROR("Failed to get baud_rate parameter");
        return false;
    }
    
    if (!private_nh_.getParam("data_bits", data_bits_)) {
        ROS_ERROR("Failed to get data_bits parameter");
        return false;
    }
    
    if (!private_nh_.getParam("stop_bits", stop_bits_)) {
        ROS_ERROR("Failed to get stop_bits parameter");
        return false;
    }
    
    if (!private_nh_.getParam("parity", parity_)) {
        ROS_ERROR("Failed to get parity parameter");
        return false;
    }
    
    // 解析串口超时参数
    if (!private_nh_.getParam("timeout", timeout_)) {
        timeout_ = 500;  // 默认500ms
        ROS_WARN("Failed to get timeout parameter, using default: %d ms", timeout_);
    }
    
    if (!private_nh_.getParam("sampling_frequency", sampling_frequency_)) {
        ROS_ERROR("Failed to get sampling_frequency parameter");
        return false;
    }
    
    if (!private_nh_.getParam("message_size", message_size_)) {
        ROS_ERROR("Failed to get message_size parameter");
        return false;
    }
    
    if (!private_nh_.getParam("topic_name", topic_name_)) {
        ROS_ERROR("Failed to get topic_name parameter");
        return false;
    }
    
    // 初始化ROS实时发布器
    wrench_pub_ = std::make_unique<realtime_tools::RealtimePublisher<geometry_msgs::WrenchStamped>>(
        nh_, topic_name_, 1);
    
    // 初始化调试统计服务
    debug_stats_service_ = nh_.advertiseService("get_debug_stats", 
                                               &FTSensorNode::getDebugStats, this);
    
    // 初始化串口
    try {
        serial_port_ = std::make_unique<serial::Serial>(
            serial_port_name_,
            baud_rate_,
            serial::Timeout::simpleTimeout(timeout_)
        );
        
        // 设置串口参数
        serial_port_->setBytesize(static_cast<serial::bytesize_t>(data_bits_));
        serial_port_->setStopbits(static_cast<serial::stopbits_t>(stop_bits_));
        serial_port_->setParity(static_cast<serial::parity_t>(parity_));
        
        ROS_INFO("Serial port initialized: %s, baud rate: %d, timeout: %d ms", 
                 serial_port_name_.c_str(), baud_rate_, timeout_);
    } catch (const serial::IOException& e) {
        ROS_ERROR("Failed to open serial port %s: %s", 
                  serial_port_name_.c_str(), e.what());
        return false;
    }
    
    ROS_INFO("FT sensor node initialized successfully");
    ROS_INFO("Topic: %s", topic_name_.c_str());
    ROS_INFO("Sampling frequency: %d Hz", sampling_frequency_);
    ROS_INFO("Serial port: %s, Baud rate: %d", serial_port_name_.c_str(), baud_rate_);
    ROS_INFO("Debug stats service: /get_debug_stats");
    
    return true;
}

void FTSensorNode::start() {
    if (running_) {
        ROS_WARN("Node is already running");
        return;
    }
    
    running_ = true;
    read_thread_ = std::thread(&FTSensorNode::run, this);
    
    // 获取并打印read_thread线程的ID
    std::thread::id thread_id = read_thread_.get_id();
    ROS_INFO("FT sensor node started, read_thread ID: %lu", std::hash<std::thread::id>{}(thread_id));
}

void FTSensorNode::stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    if (read_thread_.joinable()) {
        read_thread_.join();
    }
    
    if (serial_port_ && serial_port_->isOpen()) {
        ROS_INFO("Prepare to close serial port: %s", serial_port_name_.c_str());
        serial_port_->close();
    }
    
    ROS_INFO("FT sensor node stopped");
}

void FTSensorNode::run() {
    const auto period = std::chrono::microseconds(1000000 / sampling_frequency_);
    auto last_time = std::chrono::high_resolution_clock::now();
    
    while (running_ && ros::ok()) {
        try {
            auto start_time = std::chrono::high_resolution_clock::now();
            
            // 读取串口数据
            if (readSerialData()) {
                // 数据读取成功，继续处理
                debug_stats_.valid_packets++;
            }
            
            // 控制采样频率
            auto elapsed = std::chrono::high_resolution_clock::now() - start_time;
            auto sleep_time = period - elapsed;
            
            if (sleep_time > std::chrono::microseconds(0)) {
                std::this_thread::sleep_for(sleep_time);
            }
        } catch (const std::exception& e) {
            ROS_ERROR_THROTTLE(1.0, "Exception in run loop: %s", e.what());
            // 继续运行，不退出循环
        } catch (...) {
            ROS_ERROR_THROTTLE(1.0, "Unknown exception in run loop");
            // 继续运行，不退出循环
        }
    }
}

bool FTSensorNode::readSerialData() {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    /* 需要添加必要头文件: #include <cstddef> 用于编译器内置函数
     * __builtin_expect(condition, expected_value) 告诉编译器条件表达式的预期值
     * 编译器会优化分支预测，将正常情况（串口已打开）作为likely分支, 减少分支预测失败的概率, 提高CPU流水线效率
    */
    if (__builtin_expect(!serial_port_ || !serial_port_->isOpen(), 0)) {
        ROS_ERROR_THROTTLE(1.0, "Serial port is not open - Port: %s, Baud rate: %d", 
                          serial_port_name_.c_str(), baud_rate_);
        debug_stats_.serial_errors++;
        return false;
    }
    
    try {
        // 检查是否有可用数据
        size_t available = serial_port_->available();
        if (available == 0) {
            return false;
        }
        
        // 场景1: 数据长度刚好36字节 - 直接处理
        if (available == MESSAGE_LENGTH) {
            std::uint8_t buffer[MESSAGE_LENGTH];
            size_t bytes_read = serial_port_->read(buffer, MESSAGE_LENGTH);
            
            if (__builtin_expect(bytes_read != MESSAGE_LENGTH, 0)) {
                ROS_WARN_THROTTLE(1.0, "Failed to read complete message: expected %zu, got %zu", MESSAGE_LENGTH, bytes_read);
                debug_stats_.invalid_packets++;
                return false;
            }
            
            // 验证数据包格式
            if (buffer[0] != MESSAGE_HEADER || buffer[MESSAGE_LENGTH - 1] != MESSAGE_FOOTER) {
                ROS_WARN_THROTTLE(1.0, "Invalid message format: header=0x%02X, footer=0x%02X", 
                                 buffer[0], buffer[MESSAGE_LENGTH - 1]);
                debug_stats_.invalid_packets++;
                return false;
            }
            
            return processValidPacket(buffer);
        } else if (available < MESSAGE_LENGTH) { // 场景2: 数据长度不足36字节 - 直接丢弃
            std::uint8_t discard_buffer[available];
            serial_port_->read(discard_buffer, available);
            ROS_WARN_THROTTLE(1.0, "Discarded insufficient for complete message: %zu bytes", available);
            debug_stats_.invalid_packets++;
            return false;
        } else { // 场景3: 数据长度大于36字节 - 只处理最后一个完整消息(保证数据时效性)
            // 限制最大读取大小，避免缓冲区过大
            size_t read_size = std::min(available, MAX_READ_SIZE);
            
            // 使用栈分配，避免堆分配开销
            std::uint8_t read_buffer[MAX_READ_SIZE];
            size_t bytes_read = serial_port_->read(read_buffer, read_size);
            
            if (__builtin_expect(bytes_read != read_size, 0)) {
                ROS_WARN_THROTTLE(1.0, "Failed to read all available data: expected %zu, got %zu", read_size, bytes_read);
                debug_stats_.invalid_packets++;
                return false;
            }
            
            // 从后往前寻找最后一个完整消息
            bool message_processed = false;
            for (int i = bytes_read - MESSAGE_LENGTH; i >= 0; --i) {
                // 检查是否是消息头
                if (read_buffer[i] == MESSAGE_HEADER) {
                    // 检查消息尾
                    if (read_buffer[i + MESSAGE_LENGTH - 1] == MESSAGE_FOOTER) {
                        // 找到最后一个完整消息，直接传递指针避免memcpy
                        if (processValidPacket(&read_buffer[i])) {
                            message_processed = true;
                            break;  // 只处理最后一个找到的完整消息
                        }
                    }
                }
            }
            
            if (!message_processed) {
                ROS_WARN_THROTTLE(1.0, "No complete message found in %zu bytes, all data discarded", bytes_read);
                debug_stats_.invalid_packets++;
            }
            
            return message_processed;
        }
        
    } catch (const serial::IOException& e) {
        ROS_ERROR_THROTTLE(1.0, "Serial read error on port %s: %s", 
                          serial_port_name_.c_str(), e.what());
    } catch (const std::exception& e) {
        ROS_ERROR_THROTTLE(1.0, "Unexpected error during serial read on port %s: %s", 
                          serial_port_name_.c_str(), e.what());
    }
    
    return false;
}

bool FTSensorNode::getDebugStats(ft_sensor_node::GetDebugStats::Request& req, 
                                 ft_sensor_node::GetDebugStats::Response& res) {
    // 计算成功率
    double success_rate = (debug_stats_.total_packets_received > 0) ? 
        (double)debug_stats_.valid_packets / debug_stats_.total_packets_received * 100.0 : 0.0;
    
    // 填充响应数据
    res.total_packets_received = debug_stats_.total_packets_received;
    res.valid_packets = debug_stats_.valid_packets;
    res.invalid_packets = debug_stats_.invalid_packets;
    res.serial_errors = debug_stats_.serial_errors;
    res.publish_errors = debug_stats_.publish_errors;
    res.last_packet_time = debug_stats_.last_packet_time;
    res.success_rate = success_rate;
    
    // 打印详细的统计信息
    ROS_INFO("=== FT Sensor Debug Statistics ===");
    ROS_INFO("Total packets received: %lu", res.total_packets_received);
    ROS_INFO("Valid packets: %lu", res.valid_packets);
    ROS_INFO("Invalid packets: %lu", res.invalid_packets);
    ROS_INFO("Serial errors: %lu", res.serial_errors);
    ROS_INFO("Publish errors: %lu", res.publish_errors);
    ROS_INFO("Success rate: %.2f%%", res.success_rate);
    ROS_INFO("Target frequency: %d Hz", sampling_frequency_);
    ROS_INFO("Last packet time: %.6f", res.last_packet_time);
    ROS_INFO("==================================");
    
    return true;
}

} // namespace ft_sensor

int main(int argc, char** argv) {
    ros::init(argc, argv, "ft_sensor_node");
    
    ft_sensor::FTSensorNode node;
    
    if (!node.initialize()) {
        ROS_ERROR("Failed to initialize FT sensor node");
        return -1;
    }
    
    node.start();
    
    // 主循环
    ros::Rate rate(10); // 10 Hz control loop
    while (ros::ok()) {
        ros::spinOnce();
        rate.sleep();
    }
    
    node.stop();
    
    return 0;
} 