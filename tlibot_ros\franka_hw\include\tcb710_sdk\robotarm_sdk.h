#pragma once

#include <string>
#include <memory>
#include <thread>
#include <mutex>
#include <atomic>
#include <map>
#include <vector>
#include <functional>
#include <condition_variable>
#include <tuple>
#include <json/json.h>

#include "types.h"
#include "constants.h"

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
typedef SOCKET socket_t;
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
typedef int socket_t;
#define INVALID_SOCKET -1
#define SOCKET_ERROR -1
#endif

namespace tcb710_sdk {

class RobotArmSDK {
public:
    /**
     * @brief 构造函数
     * @param ip 机械臂IP地址
     * @param port 机械臂端口号 (6001 或 7000)
     * @param log_level 日志级别 (0: INFO, 1: DEBUG)
     */
    RobotArmSDK(const std::string& ip, int port, int log_level = 0);
    
    /**
     * @brief 析构函数
     */
    ~RobotArmSDK();
    
    // 禁用拷贝构造和赋值
    RobotArmSDK(const RobotArmSDK&) = delete;
    RobotArmSDK& operator=(const RobotArmSDK&) = delete;
    
    /**
     * @brief 连接到机械臂控制器
     * @return 连接是否成功
     */
    bool connect();
    
    /**
     * @brief 断开连接
     * @return 断开是否成功
     */
    bool disconnect();
    
    /**
     * @brief 重新连接
     * @param retries 重试次数
     * @param delay_ms 重试间隔(毫秒)
     * @return 重连是否成功
     */
    bool reconnect(int retries = 3, int delay_ms = 1000);
    
    /**
     * @brief 检查连接状态
     * @return 是否已连接
     */
    bool isConnected() const { return connect_flag_.load(); }
    
    // ========== 基础控制命令 ==========
    
    /**
     * @brief 清除伺服错误
     * @param robot 机器人号码
     * @return 清错是否成功
     */
    bool faultReset(int robot = 1);
    
    /**
     * @brief 重启控制器
     */
    void rebootController();
    
    /**
     * @brief 查询控制器初始化是否完成
     * @return 初始化是否完成
     */
    bool controllerInitFinishInquire();
    
    /**
     * @brief 设置控制器IP
     * @param robot 机器人号码
     * @param ip IP地址
     * @return 设置是否成功
     */
    bool controllerIpSet(int robot, const std::string& ip);
    
    /**
     * @brief 设置控制器IP（完整版本）
     * @param name 网口名称
     * @param address IP地址
     * @param gateway 网关
     * @param dns DNS
     * @return 设置是否成功
     */
    bool controllerIpSet(const std::string& name, const std::string& address, 
                        const std::string& gateway = "", const std::string& dns = "");
    
    /**
     * @brief 查询控制器IP
     * @param robot 机器人号码
     * @return IP地址
     */
    std::string controllerIpInquire(int robot);
    
    /**
     * @brief 查询控制器IP（完整版本）
     * @return 网络接口信息列表
     */
    std::vector<std::map<std::string, std::string>> controllerIpInquire();
    
    // ========== 运动控制命令 ==========
    
    /**
     * @brief 关节运动 MOVJ
     * @param vel 速度 (1-100)
     * @param coord 坐标模式
     * @param pos 目标位置
     * @param robot 机器人号码
     */
    void movj(int vel, CoordinateType coord, const JointAngles& pos, int robot = 1);
    
    /**
     * @brief 直线运动 MOVL
     * @param vel 速度 (mm/s, 2-1000)
     * @param coord 坐标模式
     * @param pos 目标位置
     * @param robot 机器人号码
     */
    void movl(int vel, CoordinateType coord, const JointAngles& pos, int robot = 1);
    
    /**
     * @brief 圆弧运动 MOVC
     * @param vel 速度 (mm/s, 2-1000)
     * @param coord 坐标模式
     * @param is_full 是否整圆运动
     * @param pos_one 圆弧起始点
     * @param pos_two 圆弧经过的中间点
     * @param pos_three 圆弧的目标点
     * @param robot 机器人号码
     */
    void movc(int vel, CoordinateType coord, bool is_full,
              const JointAngles& pos_one, const JointAngles& pos_two,
              const JointAngles& pos_three, int robot = 1);
    
    /**
     * @brief 样条曲线运动 MOVS
     * @param vel 速度 (mm/s, 2-1000)
     * @param coord 坐标模式
     * @param size 轨迹点数量
     * @param trajectory 轨迹点列表
     * @param robot 机器人号码
     */
    void movs(int vel, CoordinateType coord, int size, const Trajectory& trajectory, int robot = 1);
    
    // ========== 状态查询命令 ==========
    
    /**
     * @brief 查询当前位置
     * @param coord 坐标模式 (-1: 控制器当前坐标, 0: 关节坐标, 1: 直角坐标, 2: 工具坐标, 3: 用户坐标)
     * @param robot 机器人号码
     * @return 当前位置
     */
    JointAngles currentPosInquiry(CoordinateType coord, int robot = 1);
    
    /**
     * @brief 查询电机速度
     * @param robot 机器人号码
     * @return tuple<vel, maxVel> 电机速度和最大电机速度
     */
    std::tuple<double, double> currentvelInquire(int robot = 1);
    
    /**
     * @brief 查询电机扭矩
     * @param robot 机器人号码
     * @return tuple<torq, maxTorq> 电机扭矩和最大电机扭矩
     */
    std::tuple<double, double> currenttorqInquire(int robot = 1);
    
    /**
     * @brief 查询轴速度
     * @param robot 机器人号码
     * @return tuple<actualLineVel, maxActualLineVel, axisActualVel, maxAxisActualVel>
     */
    std::tuple<double, double, std::vector<double>, std::vector<double>> axisActualVelInquire(int robot = 1);
    
    /**
     * @brief 查询当前速度
     * @param robot 机器人号码
     * @return 当前速度
     */
    double speedInquire(int robot);
    
    /**
     * @brief 设置速度
     * @param speed 速度值
     * @param robot 机器人号码
     * @return 设置是否成功
     */
    bool speedSet(double speed, int robot = 1);
    
    // ========== 坐标模式控制 ==========
    
    /**
     * @brief 设置坐标模式状态
     * @param coord 坐标模式状态 (0: 关节坐标, 1: 直角坐标, 2: 工具坐标, 3: 用户坐标)
     * @param robot 机器人号码
     */
    void coordModeSet(int coord, int robot = 1);
    
    /**
     * @brief 查询坐标模式状态
     * @param robot 机器人号码
     * @return 坐标模式状态 (0: 关节坐标, 1: 直角坐标, 2: 工具坐标, 3: 用户坐标)
     */
    int coordModeInquire(int robot = 1);
    
    // ========== 运动参数控制 ==========
    
    /**
     * @brief 设置运动参数
     * @param interpolationMethod 机器人插补方式 (0: S型, 1: 梯形, 2: 加加插补)
     * @param absolutePosResolution 绝对位置分辨率 (0.0001~0.1)
     * @param runDelayTime 运行延时时间 (50~20000)
     * @param stopTime 暂停时间 (240~2000)
     */
    void interpolationModeSet(int interpolationMethod = 0, double absolutePosResolution = 0.01,
                             int runDelayTime = 500, int stopTime = 240);
    
    /**
     * @brief 查询运动参数
     * @return tuple<interpolationMethod, absolutePosResolution, runDelayTime, stopTime>
     */
    std::tuple<int, double, int, int> interpolationModeInquire();
    
    // ========== 笛卡尔坐标参数 ==========
    
    /**
     * @brief 设置笛卡尔坐标参数
     * @param MaxVel 最大速度 (1-5000, mm/s)
     * @param MaxAcc 最大加速度 (1-15, 倍数)
     * @param MaxDec 最大减速度 (-15~-1, 倍数)
     * @param MaxJerk 最大加加速度 (mm/s3)
     * @param MaxAttitudeVel 姿态运动最大速度 (1-1000, °/s)
     * @param SpeedLimitMode 速度限制方式 (0: 位姿, 1: 位置)
     */
    void decareparameterSet(int MaxVel = 1000, int MaxAcc = 3, int MaxDec = -3,
                           int MaxJerk = 10000, int MaxAttitudeVel = 500, int SpeedLimitMode = 0);
    
    /**
     * @brief 查询笛卡尔坐标参数
     * @return tuple<MaxVel, MaxAcc, MaxDec, MaxJerk, MaxAttitudeVel, SpeedLimitMode>
     */
    std::tuple<int, int, int, int, int, int> decareparameterInquire();
    
    // ========== 伺服控制命令 (7000端口) ==========
    
    /**
     * @brief 设置伺服点位运动控制
     * @param enable 是否启用
     * @param robot 机器人号码
     * @return 设置是否成功
     */
    bool setServoPointMotionControl(std::string enable, int robot = 1);
    
    /**
     * @brief 伺服点位运动控制
       @param robot(int): 机器人号码
       @param end(int):
            - 1: 停止之前的持续传输,下面的数据可不发
            - 0: 可不发 end 参数值
       @param sum(int): 总共要发的帧数
       @param count(int): 当前为第几帧
       @param PosVec(List[List[float]]): 机器人的关节角度
            - [[1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7],
                [1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7],
                ......]
     * @return 控制是否成功
     */
    bool servoPointMotionControl(int end, int sum, int count, const std::vector<JointAngles>& posVec, int robot = 1);

    /**
     * @brief 伺服运动控制（完整版本）
     * @param robot 机器人号码
     * @param end 1:停止之前的持续传输，0:可不发end参数值
     * @param sum 总共要发的帧数
     * @param count 当前为第几帧
     * @param pos_vec 机器人的关节角度
     * @return tuple<cause, success> cause为错误原因，success为是否成功
     */
    std::tuple<std::string, bool> servoPointMotionControlAdvanced(
        int robot, int end, int sum, int count, const Trajectory& pos_vec);

    /**
     * @brief 7000端口运动控制
     * @param target_vecs 轨迹运动或关节运动的目标位置
     * @return 控制是否成功
     */
    bool multiPointMove(const Trajectory& target_vecs);
    
    // ========== 队列运动控制 ==========
    
    /**
     * @brief 设置直接运动模式
     * @param enable 是否启用
     * @param robot 机器人号码
     * @return 设置是否成功
     */
    bool directMotionModeSet(bool enable, int robot = 1);
    
    /**
     * @brief 插入指令向量到队列
     * @param pos 位置
     * @param acc 加速度
     * @param dec 减速度
     * @param pl 平滑度参数
     * @param velocity 速度
     * @param imove_coord 坐标系类型
     * @param move_type 运动类型
     * @return 插入是否成功
     */
    bool directMotionInsertInstrvec(const JointAngles& pos,
                                   int acc = 30, int dec = 30, int pl = 4,
                                   int velocity = 100, const std::string& imove_coord = "RF",
                                   int move_type = 1);

    /**
     * @brief 暂停追加队列运行
     * @param robot 机器人号码
     * @return 暂停是否成功
     */
    bool directMotionModeSuspend(int robot);

    /**
     * @brief 开始追加队列运行（暂停后使用）
     * @param robot 机器人号码
     * @return 开始是否成功
     */
    bool directMotionModeStart(int robot);

    /**
     * @brief 停止追加队列运行
     * @param robot 机器人号码
     * @return 停止是否成功
     */
    bool directMotionModeStop(int robot);

    /**
     * @brief 设置队列模式停止不下电
     * @param robot 机器人号码
     * @return 设置是否成功
     */
    bool directMotionKeepPowerOn(int robot);

    // ========== 点动操作命令 ==========

    /**
     * @brief 执行点动操作
     * @param axis 代表所要操作的轴，如1代表轴1，外部轴从8开始
     * @param direction 方向：1正向，-1反向
     * @return 执行是否成功
     */
    bool jogOperationMove(int axis, int direction);

    /**
     * @brief 停止执行点动操作
     * @param axis 代表所要操作的轴，如1代表轴1，外部轴从8开始
     * @return 停止是否成功
     */
    bool jogOperationStop(int axis);

    /**
     * @brief 设置关节轴点动速度
     * @param axis_num 表示设置的关节轴
     * @param max_speed 关节轴最大点动速度，单位：度°/s
     * @param max_acc 关节轴点动加速度，单位：度°/s2
     * @return 设置是否成功
     */
    bool jogJointParameterSet(int axis_num, int max_speed, int max_acc);

    /**
     * @brief 查询关节轴点动速度
     * @param axis_num 表示需要查询的关节轴
     * @return tuple<axis_num, max_speed, max_acc>
     */
    std::tuple<int, int, int> jogJointParameterInquire(int axis_num);

    /**
     * @brief 设置点动灵敏度
     * @param sensitivity 点动灵敏度，单位度，范围0.001-1
     * @return 设置是否成功
     */
    bool jogSensitivitySet(double sensitivity);

    /**
     * @brief 查询点动灵敏度
     * @return 当前点动灵敏度，单位度，范围0.001-1
     */
    double jogSensitivityInquire();

    /**
     * @brief 设置直角坐标点动参数
     * @param MaxSpeed 直角坐标下的点动最大速度 (mm/s)
     * @param MaxAcc 直角坐标下的点动最大加速度 (mm/s2)
     */
    void jogRectparameterSet(int MaxSpeed = 200, int MaxAcc = 3000);

    /**
     * @brief 查询直角坐标点动参数
     * @return tuple<MaxSpeed, MaxAcc> 直角坐标下的点动最大速度和最大加速度
     */
    std::tuple<int, int> jogRectparameterInquire();

    // ========== 控制器配置命令 ==========

    /**
     * @brief 设置控制器通讯周期
     * @param control_cycle 参数为 1、2、4、8 毫秒(ms), 控制器重启生效
     * @return 设置是否成功
     */
    bool controlCycleSet(int control_cycle);

    /**
     * @brief 查询控制器通讯周期
     * @return 通讯周期(ms)
     */
    int controlCycleInquire();

    /**
     * @brief 设置伺服上下电状态
     * @param deadman 上下电状态 (0: 下电状态, 1: 上电状态)
     * @return 设置的状态值
     */
    int deadmanStatusSet(int deadman);

    /**
     * @brief 查询伺服上下电状态
     * @return 上下电状态 (0: 下电状态, 1: 上电状态)
     */
    int deadmanStatusInquire();

    /**
     * @brief 设置伺服状态
     * @param robot 机器人号码
     * @param status 伺服状态 (0: 伺服下电, 1: 伺服上电, 2: 伺服使能, 3: 伺服运行)
     * @return 设置的状态值
     */
    int servoStatusSet(int robot, int status);

    /**
     * @brief 查询伺服状态
     * @param robot 机器人号码
     * @return 伺服状态 (0: 伺服下电, 1: 伺服上电, 2: 伺服使能, 3: 伺服运行)
     */
    int servoStatusInquire(int robot);

    /**
     * @brief 查询伺服连接状态
     * @return 伺服类型 (1: 虚拟伺服, 2: 无伺服, 其他: 实际伺服)
     */
    int servoConnectInquire();

    /**
     * @brief 查询伺服参数
     * @param robot 机器人号码
     * @param servo_num 伺服编号 (1-7)
     * @return tuple<error_code, encoder_status_register1, encoder_status_register2, encoder_single_turn_value, holding_brake_status>
     */
    std::tuple<int, int, int, int, int> servoInsideParmInquire(int robot, int servo_num);

    /**
     * @brief 伺服参数设置
     * @param servo_num 伺服编号 (1-7)
     * @param key_name 参数名称
     * @param key_value 参数值
     * @param temporary_save 是否临时保存 (1: 临时保存, 0: 永久保存)
     * @param robot 机器人号码
     * @return 设置是否成功
     */
    bool servoInsideParmSet(int servo_num, const std::string& key_name,
                           int key_value, int temporary_save = 1, int robot = 1);

    /**
     * @brief 查询从站列表
     * @return tuple<io_num, servo_num, slave_type, slave_type_english>
     */
    std::tuple<int, int, std::string, std::string> slavetypeListRespond();

    /**
     * @brief 设置操作模式状态
     * @param mode 操作模式 (0: 示教模式(Teach), 1: 再现模式(Play), 2: 运行模式(Repeat))
     * @return 设置是否成功
     */
    bool operationModeSet(int mode);

    /**
     * @brief 查询操作模式状态
     * @return 操作模式 (0: 示教模式(Teach), 1: 再现模式(Play), 2: 运行模式(Repeat))
     */
    int operationModeInquire();

    /**
     * @brief 设置关节参数
     * @param axis_num 关节轴数
     * @param pos_sw_limit 关节正限位
     * @param neg_sw_limit 关节反限位
     * @param direction 模型方向 (默认为1)
     * @return 设置是否成功
     */
    bool jointParameterSet(int axis_num, double pos_sw_limit, double neg_sw_limit, int direction = 1);

    /**
     * @brief 查询关节参数
     * @param axis_num 关节轴数 (1-7 分别代表关节1-7)
     * @return tuple<pos_sw_limit, neg_sw_limit, rated_rot_speed, rated_de_rot_speed, rated_vel, de_rated_vel>
     */
    std::tuple<double, double, double, double, double, double> jointParameterInquire(int axis_num);

    // ========== 作业控制命令 ==========

    /**
     * @brief 开始运行作业文件
     * @param job_name 作业文件名
     * @param line 起始行号
     * @param continue_run 是否继续运行
     * @param robot 机器人号码
     * @return 执行是否成功
     */
    bool jobSendDone(const std::string& job_name, int line, int continue_run, int robot = 1);

    /**
     * @brief 停止正在运行的作业文件
     * @param robot 机器人号码
     * @return 停止是否成功
     */
    bool stopJobRun(int robot);

    /**
     * @brief 获取作业文件列表
     * @return 作业文件列表
     */
    std::vector<std::string> jobFileListInquire();

private:
    // 网络相关
    std::string ip_;
    int port_;
    socket_t socket_;
    std::atomic<bool> connect_flag_;
    
    // 线程相关
    std::unique_ptr<std::thread> receive_thread_;
    std::mutex status_mutex_;
    std::condition_variable status_cv_;
    
    // 状态存储
    StatusMap return_status_;
    
    // 内部方法
    void startReceiveThread();
    void receiveLoop();
    void parseStatus(const std::string& data, const std::string& cmd_word);
    
    bool sendCommand(uint16_t cmd_word, const Json::Value& cmd_data = Json::Value::null);
    std::vector<uint8_t> createFrame(uint16_t cmd_word, const std::string& data);
    uint32_t calculateCRC32(const std::vector<uint8_t>& data);
    
    std::string waitForResponse(const std::string& key, int timeout_ms = 5000);
    bool waitForBoolResponse(const std::string& key, int timeout_ms = 5000);
    
    // 平台相关的socket初始化和清理
    bool initializeSocket();
    void cleanupSocket();
    
#ifdef _WIN32
    static bool wsa_initialized_;
#endif
};

} // namespace tcb710_sdk
