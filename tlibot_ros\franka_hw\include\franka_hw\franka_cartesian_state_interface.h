// Copyright (c) 2024 FDR2 Robot
// Use of this source code is governed by the Apache-2.0 license, see LICENSE
#pragma once

#include <string>
#include <array>
#include <hardware_interface/internal/hardware_resource_manager.h>

namespace franka_hw {

/**
 * Handle to read Cartesian state information.
 */
class CartesianStateHandle {
public:
    CartesianStateHandle(const std::string& name,
                        const std::array<double, 16>& pose,
                        const std::array<double, 6>& velocity,
                        const std::array<double, 6>& acceleration)
        : name_(name), pose_(&pose), velocity_(&velocity), acceleration_(&acceleration) {}
    
    const std::array<double, 16>& getPose() const { return *pose_; }
    const std::array<double, 6>& getVelocity() const { return *velocity_; }
    const std::array<double, 6>& getAcceleration() const { return *acceleration_; }

private:
    std::string name_;
    const std::array<double, 16>* pose_;
    const std::array<double, 6>* velocity_;
    const std::array<double, 6>* acceleration_;
};

/**
 * Hardware interface to read Cartesian state.
 */
class CartesianStateInterface
    : public hardware_interface::HardwareResourceManager<CartesianStateHandle> {};

}  // namespace franka_hw