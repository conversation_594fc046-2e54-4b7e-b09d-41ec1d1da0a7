// Copyright (c) 2017 Franka Emika GmbH
// Use of this source code is governed by the Apache-2.0 license, see LICENSE
#include <franka_gripper/jodellgripper.h>
#include <jodellTool.h>

#include <sstream>
#include <algorithm>
#include <franka/exception.h>
#include <franka/gripper_state.h>
//#include <research_interface/gripper/types.h>

//#include "network.h"

namespace jodell {

namespace {

// template <typename T, typename... TArgs>
// bool executeCommand(Network& network, TArgs&&... args) {
//   uint32_t command_id = network.tcpSendRequest<T>(std::forward<TArgs>(args)...);
//   typename T::Response response = network.tcpBlockingReceiveResponse<T>(command_id);

//   switch (response.status) {
//     case T::Status::kSuccess:
//       return true;
//     case T::Status::kFail:
//       throw CommandException("libfranka gripper: Command failed!");
//     case T::Status::kUnsuccessful:
//       return false;
//     case T::Status::kAborted:
//       throw CommandException("libfranka gripper: Command aborted!");
//     default:
//       throw ProtocolException("libfranka gripper: Unexpected response while handling command!");
//   }
// }

// GripperState convertGripperState(
//     const research_interface::gripper::GripperState& gripper_state) noexcept {
//   GripperState converted;
//   converted.width = gripper_state.width;
//   converted.max_width = gripper_state.max_width;
//   converted.is_grasped = gripper_state.is_grasped;
//   converted.temperature = gripper_state.temperature;
//   converted.time = Duration(gripper_state.message_id);
//   return converted;
// }

}  // anonymous namespace

// Gripper::Gripper(const std::string& franka_address)
//     : network_{
//           std::make_unique<Network>(franka_address, research_interface::gripper::kCommandPort)} {
//   connect<research_interface::gripper::Connect, research_interface::gripper::kVersion>(
//       *network_, &ri_version_);
// }

Gripper::Gripper(){
    //todo
  ri_version_ = 0;    
  slave_id = 9;     //夹爪站点id，默认为9
  com_index = 0;     //串口编号（0-7）
 
}

Gripper::~Gripper() noexcept = default;
Gripper::Gripper(Gripper&&) noexcept = default;
Gripper& Gripper::operator=(Gripper&&) noexcept = default;

Gripper::ServerVersion Gripper::serverVersion() const noexcept {
  return ri_version_;
}

int Gripper::init() {
    int* comList = getComPort ();
  std::stringstream ss;
    ss << "COM list: ";
    for (int i = 0; i < 8; ++i) 
    {
        ss << comList[i] << " ";
        if(0 == comList[i])             //TODO 假定只有1个, 后面要调试, 看怎么定, 是不是固定了
        {
            com_index = i;
            int status = serialOperation(com_index, com_index+1, 115200, true);
            if(1 != status)
            {
                ROS_ERROR("i:%d serialOperation error:%d", i, status);
            }
            ROS_INFO("i:%d serialOperation success:%d", i, status);
        }
    }
    ROS_INFO_STREAM(ss.str());
    return 0;
}

/*
1.0x00 表示完全打开，0xFF 表示完全闭合，两指之间线性关系。
2. 为了避免夹取状态位置识别错误，需结合电动夹爪的最大行程与夹取物体
的最大行程来确定夹取的位置，例如：EPG50-060，这里假设电动夹爪需夹
取的物体尺寸为 10mm，EPG50-060 电动夹爪的最大行程为 50mm，我司提
供 EPG50-060 最大位置对应写入值为 255(完全闭合)，最小值为 0（完全打
开），对应比例关系得出该物体 10mm 对应 EPG50-060 的夹取位置为 204
即可夹取到物体，但是为了保证电动夹爪夹取物体以后能够正常反馈状态，
需要将刚刚好能够夹取到物体的位置设置超过 6（即最大值 255 的 2%）以上，
所以将这里的夹取位置设置到 210 以上电动夹爪才能正常反馈状态。
*/

//将夹爪归零并根据已安装的手指更新最大宽度。
bool Gripper::homing() const {
    int status = runWithParam(com_index, slave_id, 0, 100, 0);
    if(1 != status) 
    {
        ROS_ERROR("homing failed! status:%d", status);
        return false;
    }
    return true;
}

bool Gripper::grasp(double width,               // m
                    double speed,               // m/s
                    double force,               // N
                    double epsilon_inner,
                    double epsilon_outer) const {
    width = std::min(std::max(0.0, width), MaxWidth);
    speed = std::min(std::max(0.0, speed), MaxSpeed);
    force = std::min(std::max(MinTorque, force), MaxTorque);
    int run_pos = (MaxWidth - width) / MaxWidth * 255 + 6;
    int run_speed = speed / MaxSpeed * 255;
    int run_torque = (force - MinTorque) / (MaxTorque - MinTorque) * 255;               //按最小最大量程40~100N之间转换0~255
    int status = runWithParam(com_index, slave_id, run_pos, run_speed, run_torque);
    ROS_INFO("status:%d \n"
              "width:%f speed:%f force:%f epsilon_inner:%f epsilon_outer:%f \n"
              "run_pos:%d run_speed:%d run_torque:%d",
              status,
              width, speed, force, epsilon_inner, epsilon_outer,
              run_pos, run_speed, run_torque);
    if(1 != status)
    {
        ROS_ERROR("grasp failed! status:%d", status);
        return false;
    }
    return true;
}

bool Gripper::move(double width, double speed) const {
    return grasp(width, speed, 0, 0, 0);
}

bool Gripper::stop() const {
    jodell::GripperState state = readOnce();
    return grasp(state.width, 0, 0, 0, 0);
}

jodell::GripperState Gripper::readOnce() const {

  //todo  调用钧舵API获取状态，转换为GripperState
  jodell::GripperState state;  
  state.status_errcode = 0;

  //夹爪的最大宽度，直接取钧舵夹爪的最大量程40mm
  state.max_width = MaxWidth;

  //获取夹爪状态
  int current_status = getClawCurrentStatus(com_index, slave_id);
  if(-1 == current_status)
  {
    state.status_errcode = static_cast<int32_t>(GripperErrorCode::STATUS_ERROR);  //获取夹爪状态失败
  }
  if(1 == current_status || 2 == current_status)
  {
    state.is_grasped = true;
  }

  //从钧舵SDK获取位置，换算为width
  int current_location = getClawCurrentLocation(com_index, slave_id);
  if(current_location < 0 || current_location > 0xFF)
  {
    state.status_errcode = static_cast<int32_t>(GripperErrorCode::POSITION_ERROR);  //获取夹爪位置失败
    current_location = 0;  //异常状态，夹爪位置返回最大
  }
  current_location = current_location & 0xFF;
  state.width  = (0xFF - current_location) * MaxWidth / 0xFF;  //从0-255标幺值，转换为0.04m-0物理长度
  
  //获取夹爪速度
  int current_speed  = getClawCurrentSpeed(com_index, slave_id);
  if(current_speed < 0 || current_speed > 0xFF)
  {
    state.status_errcode = static_cast<int32_t>(GripperErrorCode::SPEED_ERROR);  //获取夹爪速度失败
  }
  current_speed = current_speed &0xFF;  
  state.speed = current_speed * MaxSpeed / 0xFF;  //从0-255标幺值，转换为0-MaxSpeed物理速度

  //获取夹爪力矩
  int current_torque = getClawCurrentTorque(com_index, slave_id);
  if(current_torque < 0 || current_torque > 0xFF)  
  {
    state.status_errcode = static_cast<int32_t>(GripperErrorCode::TORQUE_ERROR);  //获取夹爪力矩失败
  }
  current_torque = current_torque & 0xFF;
  state.torque = current_torque * (MaxTorque - MinTorque) / 0xFF + MinTorque;  //从0-255标幺值，转换为40-100N物理力矩

  //获取夹爪温度
  int current_temperature = getClawCurrentTemperature(com_index, slave_id);
  if(-1 == current_temperature)
  {
    state.status_errcode = static_cast<int32_t>(GripperErrorCode::TEMPERATURE_ERROR);  //获取夹爪温度失败
  }
  state.temperature = current_temperature & 0xFF;   //环境温度占用 1byte，按照有符号 8 位数据解析

  state.time = franka::Duration(0);

  // state.width = 0;
  // state.max_width = 0;
  // state.is_grasped = 0;
  // state.temperature = 0;
  // state.speed = 0;
  // state.torque = 0;

  return state;
}

}  // namespace jodell
