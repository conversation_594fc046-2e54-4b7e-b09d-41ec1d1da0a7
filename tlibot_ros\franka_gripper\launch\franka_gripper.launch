<?xml version="1.0" ?>
<launch>
  <!-- <arg name="robot_ip" /> -->
  <arg name="stop_at_shutdown" default="false" />
  <arg name="arm_id"      default="panda" />
  <arg name="joint_names" default="[$(arg arm_id)_finger_joint1, $(arg arm_id)_finger_joint2]" />

  <node name="franka_gripper" pkg="franka_gripper" type="franka_gripper_node" output="screen">
    <!-- <param name="robot_ip" value="$(arg robot_ip)"/> -->
    <param name="stop_at_shutdown" value="$(arg stop_at_shutdown)" />
    <rosparam command="load" file="$(find franka_gripper)/config/franka_gripper_node.yaml" />
    <rosparam param="joint_names" subst_value="true">$(arg joint_names)</rosparam>
  </node>

</launch>
