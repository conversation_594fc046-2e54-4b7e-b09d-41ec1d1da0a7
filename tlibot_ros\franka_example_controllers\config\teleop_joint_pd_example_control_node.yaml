robot_hardware:
  - leader
  - follower

leader:
  type: franka_hw/FrankaCombinableHW
  arm_id: leader
  joint_names:
    - leader_joint1
    - leader_joint2
    - leader_joint3
    - leader_joint4
    - leader_joint5
    - leader_joint6
    - leader_joint7
  # Configure the threshold angle for printing joint limit warnings.
  joint_limit_warning_threshold: 0.1 # [rad]
  # Activate rate limiter? [true|false]
  rate_limiting: true
  # Cutoff frequency of the low-pass filter. Set to >= 1000 to deactivate.
  cutoff_frequency: 1000
  # Internal controller for motion generators [joint_impedance|cartesian_impedance]
  internal_controller: joint_impedance
  # Used to decide whether to enforce realtime mode [enforce|ignore]
  realtime_config: enforce
  # Configure collision behavior reflexes.
  # The upper torque and upper force thresholds are set to higher values compared to the default ones.
  collision_config:
    lower_torque_thresholds_acceleration: [25.0, 25.0, 23.0, 23.0, 20.0, 17.5, 15.0]  # [Nm]
    upper_torque_thresholds_acceleration: [25.0, 25.0, 23.0, 23.0, 20.0, 17.5, 15.0]  # [Nm]
    lower_torque_thresholds_nominal: [25.0, 25.0, 23.0, 23.0, 20.0, 17.5, 15.0] # [Nm]
    upper_torque_thresholds_nominal: [25.0, 25.0, 23.0, 23.0, 20.0, 17.5, 15.0] # [Nm]
    lower_force_thresholds_acceleration: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]  # [N, N, N, Nm, Nm, Nm]
    upper_force_thresholds_acceleration: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]  # [N, N, N, Nm, Nm, Nm]
    lower_force_thresholds_nominal:   [40.0, 40.0, 40.0, 40.0, 40.0, 40.0] # [N, N, N, Nm, Nm, Nm]
    upper_force_thresholds_nominal:   [40.0, 40.0, 40.0, 40.0, 40.0, 40.0] # [N, N, N, Nm, Nm, Nm]

follower:
  type: franka_hw/FrankaCombinableHW
  arm_id: follower
  joint_names:
    - follower_joint1
    - follower_joint2
    - follower_joint3
    - follower_joint4
    - follower_joint5
    - follower_joint6
    - follower_joint7
  # Configure the threshold angle for printing joint limit warnings.
  joint_limit_warning_threshold: 0.1 # [rad]
  # Activate rate limiter? [true|false]
  rate_limiting: true
  # Cutoff frequency of the low-pass filter. Set to >= 1000 to deactivate.
  cutoff_frequency: 1000
  # Internal controller for motion generators [joint_impedance|cartesian_impedance]
  internal_controller: joint_impedance
  # Used to decide whether to enforce realtime mode [enforce|ignore]
  realtime_config: enforce
  # Configure collision behavior reflexes.
  # The upper torque and upper force thresholds are set to higher values compared to the default ones.
  collision_config:
    lower_torque_thresholds_acceleration: [25.0, 25.0, 23.0, 23.0, 20.0, 17.5, 15.0]  # [Nm]
    upper_torque_thresholds_acceleration: [25.0, 25.0, 23.0, 23.0, 20.0, 17.5, 15.0]  # [Nm]
    lower_torque_thresholds_nominal: [25.0, 25.0, 23.0, 23.0, 20.0, 17.5, 15.0] # [Nm]
    upper_torque_thresholds_nominal: [25.0, 25.0, 23.0, 23.0, 20.0, 17.5, 15.0] # [Nm]
    lower_force_thresholds_acceleration: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]  # [N, N, N, Nm, Nm, Nm]
    upper_force_thresholds_acceleration: [40.0, 40.0, 40.0, 40.0, 40.0, 40.0]  # [N, N, N, Nm, Nm, Nm]
    lower_force_thresholds_nominal:   [40.0, 40.0, 40.0, 40.0, 40.0, 40.0] # [N, N, N, Nm, Nm, Nm]
    upper_force_thresholds_nominal:   [40.0, 40.0, 40.0, 40.0, 40.0, 40.0] # [N, N, N, Nm, Nm, Nm]
