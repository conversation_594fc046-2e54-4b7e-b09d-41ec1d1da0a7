#!/usr/bin/env python
PACKAGE = "franka_example_controllers"

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

gen.add("translational_stiffness", double_t, 0, "Cartesian translational stiffness", 200, 0, 400)
gen.add("rotational_stiffness", double_t, 0, "Cartesian rotational stiffness", 10, 0, 30)
gen.add("nullspace_stiffness", double_t, 0, "Stiffness of the joint space nullspace controller (the desired configuration is the one at startup)", 0.5, 0, 100)

exit(gen.generate(PACKAGE, "dynamic_compliance", "compliance_param"))
