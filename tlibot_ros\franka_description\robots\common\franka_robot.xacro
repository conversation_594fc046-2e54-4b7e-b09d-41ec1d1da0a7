<?xml version='1.0' encoding='utf-8'?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
  <xacro:macro name="franka_robot" params="arm_id joint_limits">
    <!-- Name of this panda -->
    <!-- Should a franka_gripper be mounted at the flange?" -->
    <xacro:arg name="hand" default="false" />
    <!-- Positional offset between $(arm_id)_hand -> $(arm_id)_hand_tcp [m]. Only used when hand:=true -->
    <xacro:arg name="tcp_xyz" default="0 0 0.1034" />
    <!-- Rotational offset between $(arm_id)_hand -> $(arm_id)_hand_tcp [rad]. Only used when hand:=true -->
    <xacro:arg name="tcp_rpy" default="0 0 0" />
    <!-- Is the robot being simulated in gazebo?" -->
    <xacro:arg name="gazebo" default="false" />

    <xacro:include filename="$(find franka_description)/robots/common/utils.xacro" />
    <xacro:include filename="$(find franka_description)/robots/common/franka_arm.xacro" />

    <xacro:franka_arm arm_id="${arm_id}" safety_distance="0.03" gazebo="$(arg gazebo)" joint_limits="${joint_limits}"/>

    <xacro:if value="$(arg hand)">
      <xacro:include filename="$(find franka_description)/robots/common/franka_hand.xacro"/>
      <xacro:franka_hand
          arm_id="$(arg arm_id)"
          rpy="0 0 ${-pi/4}"
          tcp_xyz="$(arg tcp_xyz)"
          tcp_rpy="$(arg tcp_rpy)"
          connected_to="$(arg arm_id)_link8"
          safety_distance="0.03"
          gazebo="$(arg gazebo)"
       />
    </xacro:if>

    <!-- Define additional Gazebo tags -->
    <xacro:if value="$(arg gazebo)">

      <xacro:arg name="xyz" default="0 0 0" />
      <xacro:arg name="rpy" default="0 0 0" />

      <!-- Gazebo requires a joint to a link called "world" for statically mounted robots -->
      <link name="world" />
      <joint name="world_joint" type="fixed">
        <origin xyz="$(arg xyz)" rpy="$(arg rpy)" />
        <parent link="world" />
        <child  link="$(arg arm_id)_link0" />
      </joint>

      <xacro:gazebo-joint joint="${arm_id}_joint1" transmission="hardware_interface/PositionJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint2" transmission="hardware_interface/PositionJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint3" transmission="hardware_interface/PositionJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint4" transmission="hardware_interface/PositionJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint5" transmission="hardware_interface/PositionJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint6" transmission="hardware_interface/PositionJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint7" transmission="hardware_interface/PositionJointInterface" />

      <xacro:gazebo-joint joint="${arm_id}_joint1" transmission="hardware_interface/VelocityJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint2" transmission="hardware_interface/VelocityJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint3" transmission="hardware_interface/VelocityJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint4" transmission="hardware_interface/VelocityJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint5" transmission="hardware_interface/VelocityJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint6" transmission="hardware_interface/VelocityJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint7" transmission="hardware_interface/VelocityJointInterface" />

      <xacro:gazebo-joint joint="${arm_id}_joint1" transmission="hardware_interface/EffortJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint2" transmission="hardware_interface/EffortJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint3" transmission="hardware_interface/EffortJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint4" transmission="hardware_interface/EffortJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint5" transmission="hardware_interface/EffortJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint6" transmission="hardware_interface/EffortJointInterface" />
      <xacro:gazebo-joint joint="${arm_id}_joint7" transmission="hardware_interface/EffortJointInterface" />

      <xacro:transmission-franka-state arm_id="${arm_id}" />
      <xacro:transmission-franka-model arm_id="${arm_id}"
         root="${arm_id}_joint1"
         tip="${arm_id}_joint8"
       />

      <xacro:if value="$(arg hand)">
        <xacro:gazebo-joint joint="${arm_id}_finger_joint1" transmission="hardware_interface/EffortJointInterface" />
        <xacro:gazebo-joint joint="${arm_id}_finger_joint2" transmission="hardware_interface/EffortJointInterface" />
        <!-- Friction specific material for Rubber/Rubber contact -->
        <xacro:gazebo-friction link="${arm_id}_leftfinger" mu="1.13" />
        <xacro:gazebo-friction link="${arm_id}_rightfinger" mu="1.13" />
      </xacro:if>

      <gazebo>
        <plugin name="gazebo_ros_control" filename="libgazebo_ros_control.so">
          <controlPeriod>0.001</controlPeriod>
          <robotSimType>franka_gazebo/FrankaHWSim</robotSimType>
        </plugin>
        <self_collide>true</self_collide>
      </gazebo>
    </xacro:if>
  </xacro:macro>
</robot>
