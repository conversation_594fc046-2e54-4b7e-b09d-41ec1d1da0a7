<library path="lib/libfranka_example_controllers">
  <class name="franka_example_controllers/JointVelocityExampleController" type="franka_example_controllers::JointVelocityExampleController" base_class_type="controller_interface::ControllerBase">
    <description>
      A controller that executes a short motion based on joint velocities to demonstrate correct usage
    </description>
  </class>
  <class name="franka_example_controllers/JointPositionExampleController" type="franka_example_controllers::JointPositionExampleController" base_class_type="controller_interface::ControllerBase">
    <description>
      A controller that executes a short motion based on joint positions to demonstrate correct usage
    </description>
  </class>
  <class name="franka_example_controllers/CartesianPoseExampleController" type="franka_example_controllers::CartesianPoseExampleController" base_class_type="controller_interface::ControllerBase">
    <description>
      A controller that executes a short motion based on cartesian poses to demonstrate correct usage
    </description>
  </class>
  <class name="franka_example_controllers/CartesianVelocityExampleController" type="franka_example_controllers::CartesianVelocityExampleController" base_class_type="controller_interface::ControllerBase">
    <description>
      A controller that executes a short motion based on cartesian velocities to demonstrate correct usage
    </description>
  </class>
  <class name="franka_example_controllers/CartesianStateTestController" type="franka_example_controllers::CartesianStateTestController" base_class_type="controller_interface::ControllerBase">
    <description>
      A test controller that reads and publishes Cartesian state information (pose, velocity, acceleration) to verify CartesianStateInterface functionality
    </description>
  </class>
  <class name="franka_example_controllers/ElbowExampleController" type="franka_example_controllers::ElbowExampleController" base_class_type="controller_interface::ControllerBase">
    <description>
      A controller that executes a short motion based on cartesian poses and elbow to demonstrate correct usage
    </description>
  </class>
  <class name="franka_example_controllers/ModelExampleController" type="franka_example_controllers::ModelExampleController" base_class_type="controller_interface::ControllerBase">
    <description>
      A controller that evaluates and prints the dynamic model of Franka
    </description>
  </class>
  <class name="franka_example_controllers/JointImpedanceExampleController" type="franka_example_controllers::JointImpedanceExampleController" base_class_type="controller_interface::ControllerBase">
    <description>
      A controller that tracks a cartesian path with a joint impedance controller that compensates coriolis torques. The torque commands are compared to measured torques in Console outputs.
    </description>
  </class>
  <class name="franka_example_controllers/CartesianImpedanceExampleController" type="franka_example_controllers::CartesianImpedanceExampleController" base_class_type="controller_interface::ControllerBase">
    <description>
      A controller that renders a spring damper system in cartesian space. Compliance parameters and the equilibrium pose can be modified online with dynamic reconfigure and an interactive marker respectively.
    </description>
  </class>
  <class name="franka_example_controllers/ForceExampleController" type="franka_example_controllers::ForceExampleController" base_class_type="controller_interface::ControllerBase">
    <description>
      A PI controller that applies a force corresponding to a user-provided desired mass in the z axis. The desired mass value can be modified online with dynamic reconfigure.
    </description>
  </class>
  <class name="franka_example_controllers/DualArmCartesianImpedanceExampleController" type="franka_example_controllers::DualArmCartesianImpedanceExampleController" base_class_type="controller_interface::ControllerBase">
      <description>
          A controller that renders Cartesian impedance based tracking of separate target poses for 2 panda arms.
      </description>
  </class>
  <class name="franka_example_controllers/TeleopJointPDExampleController" type="franka_example_controllers::TeleopJointPDExampleController" base_class_type="controller_interface::ControllerBase">
      <description>
          A PD controller that tracks position and velocity of the leader arm and applies it to the follower arm. It also applies force-feedback to the leader arm.
      </description>
  </class>
</library>
