<?xml version="1.0" encoding="utf-8"?>
<package format="2">
  <name>ft_sensor_node</name>
  <version>1.0.0</version>
  <description>六维力传感器ROS节点, 通过串口读取传感器数据并发布到实时话题</description>

  <maintainer email="<EMAIL>">张森10071623</maintainer>
  <license>Apache 2.0</license>

  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>message_generation</build_depend>
  <build_depend>realtime_tools</build_depend>
  <build_depend>serial</build_depend>
  
  <exec_depend>roscpp</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>message_runtime</exec_depend>
  <exec_depend>realtime_tools</exec_depend>
  <exec_depend>serial</exec_depend>

  <export>
  </export>
</package> 