<?xml version="1.0"?>
<package format="2">
  <name>franka_example_controllers</name>
  <version>0.10.1</version>
  <description>franka_example_controllers provides example code for controlling Franka Emika research robots with ros_control</description>
  <maintainer email="<EMAIL>">Franka Emika GmbH</maintainer>
  <license>Apache 2.0</license>

  <url type="website">http://wiki.ros.org/franka_example_controllers</url>
  <url type="repository">https://github.com/frankaemika/franka_ros</url>
  <url type="bugtracker">https://github.com/frankaemika/franka_ros/issues</url>
  <author>Franka Emika GmbH</author>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>message_generation</build_depend>
  <build_depend>eigen</build_depend>

  <build_export_depend>message_runtime</build_export_depend>

  <depend>controller_interface</depend>
  <depend>dynamic_reconfigure</depend>
  <depend>eigen_conversions</depend>
  <depend>franka_hw</depend>
  <depend>franka_gripper</depend>
  <depend>geometry_msgs</depend>
  <depend>hardware_interface</depend>
  <depend>joint_limits_interface</depend>
  <depend>tf</depend>
  <depend>tf_conversions</depend>
  <depend>libfranka</depend>
  <depend>pluginlib</depend>
  <depend>realtime_tools</depend>
  <depend>roscpp</depend>
  <depend>urdf</depend>
  <depend>visualization_msgs</depend>

  <exec_depend>franka_control</exec_depend>
  <exec_depend>franka_description</exec_depend>
  <exec_depend>message_runtime</exec_depend>
  <exec_depend>rospy</exec_depend>

  <export>
    <controller_interface plugin="${prefix}/franka_example_controllers_plugin.xml" />
  </export>
</package>
