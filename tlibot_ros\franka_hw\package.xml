<?xml version="1.0"?>
<package format="2">
  <name>franka_hw</name>
  <version>0.10.1</version>
  <description>franka_hw provides hardware interfaces for using Franka Emika research robots with ros_control</description>
  <maintainer email="<EMAIL>">Franka Emika GmbH</maintainer>
  <license>Apache 2.0</license>

  <url type="website">http://wiki.ros.org/franka_hw</url>
  <url type="repository">https://github.com/frankaemika/franka_ros</url>
  <url type="bugtracker">https://github.com/frankaemika/franka_ros/issues</url>
  <author>Franka Emika GmbH</author>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>message_generation</build_depend>

  <depend>actionlib_msgs</depend>
  <depend>actionlib</depend>
  <depend>controller_interface</depend>
  <depend>combined_robot_hw</depend>
  <depend>hardware_interface</depend>
  <depend>joint_limits_interface</depend>
  <depend>libfranka</depend>
  <depend>roscpp</depend>
  <depend>std_srvs</depend>
  <depend>urdf</depend>
  <depend>pluginlib</depend>
  <depend>franka_msgs</depend>

  <test_depend>franka_description</test_depend>
  <test_depend>gtest</test_depend>
  <test_depend>rostest</test_depend>

  <export>
      <hardware_interface plugin="${prefix}/franka_combinable_hw_plugin.xml"/>
  </export>
</package>
