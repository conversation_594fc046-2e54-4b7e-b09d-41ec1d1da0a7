# FT Sensor Node 详细设计文档

## 1. 系统概述

### 1.1 功能描述
FT Sensor Node是一个ROS节点，用于通过串口读取六维力传感器数据，并将数据发布为ROS话题。系统支持实时数据采集、数据验证、错误处理和调试统计功能。

### 1.2 技术栈
- **ROS**: 机器人操作系统
- **C++11/14**: 核心编程语言
- **Serial库**: 串口通信
- **Realtime Tools**: 实时消息发布
- **Python**: 调试脚本和测试工具

## 2. 正常业务流程设计

### 2.1 系统启动流程
```
1. 节点初始化
   ├── 读取配置文件参数
   ├── 初始化串口连接
   ├── 设置串口参数（波特率、数据位、停止位、校验位、超时）
   ├── 初始化ROS实时发布器
   └── 启动调试统计服务

2. 启动数据采集线程
   ├── 创建独立的数据采集线程
   ├── 设置线程运行标志
   └── 开始数据采集循环

3. 主循环
   ├── ROS消息循环
   ├── 响应ROS信号
   └── 控制节点生命周期
```

### 2.2 数据采集流程
```
1. 串口数据读取
   ├── 检查串口状态
   ├── 检查可用数据量
   ├── 读取固定长度数据包（36字节）
   └── 验证数据完整性

2. 数据包验证
   ├── 检查消息头（0xA5）
   ├── 检查消息尾（0x0D）
   └── 验证数据包长度

3. 数据解析
   ├── 解析时间戳（字节1-4）
   ├── 解析力数据（Fx, Fy, Fz）
   ├── 解析力矩数据（Mx, My, Mz）
   └── 解析温度数据（字节29-32）

4. 消息发布
   ├── 获取实时发布器锁
   ├── 设置消息头信息
   ├── 填充力/力矩数据
   ├── 解锁并发布消息
   └── 更新统计信息
```

### 2.3 频率控制机制
```
1. 采样频率控制
   ├── 计算目标周期（1/采样频率）
   ├── 测量实际处理时间
   ├── 计算需要睡眠的时间
   └── 精确睡眠控制

2. 实时性保证
   ├── 使用高精度时钟
   ├── 微秒级时间控制
   └── 避免过度睡眠
```

## 3. 异常场景设计

### 3.1 串口通信异常
**场景描述**：
- 串口设备断开连接
- 串口权限不足
- 串口参数配置错误
- 数据包格式错误

**处理策略**：
```cpp
// 串口状态检查
if (!serial_port_ || !serial_port_->isOpen()) {
    ROS_ERROR_THROTTLE(1.0, "Serial port is not open");
    debug_stats_.serial_errors++;
    return false;
}

// 异常捕获
try {
    // 串口操作
} catch (const serial::IOException& e) {
    ROS_ERROR_THROTTLE(1.0, "Serial read error: %s", e.what());
    debug_stats_.serial_errors++;
} catch (const std::exception& e) {
    ROS_ERROR_THROTTLE(1.0, "Unexpected error: %s", e.what());
}
```

### 3.2 数据包异常
**场景描述**：
- 数据包不完整
- 数据包格式错误
- 消息头/尾验证失败

**处理策略**：
```cpp
// 数据包完整性检查
if (bytes_read != MESSAGE_LENGTH) {
    ROS_WARN_THROTTLE(1.0, "Incomplete data received");
    debug_stats_.invalid_packets++;
    return false;
}

// 数据包格式验证
if (buffer[0] != MESSAGE_HEADER || 
    buffer[MESSAGE_LENGTH - 1] != MESSAGE_FOOTER) {
    ROS_WARN_THROTTLE(1.0, "Invalid packet received");
    debug_stats_.invalid_packets++;
    return false;
}
```

### 3.3 发布器异常
**场景描述**：
- 实时发布器不可用
- 发布器锁获取失败
- 网络连接问题

**处理策略**：
```cpp
// 发布器状态检查
if (wrench_pub_ && wrench_pub_->trylock()) {
    // 正常发布流程
} else {
    debug_stats_.publish_errors++;
    return false;
}
```

### 3.4 系统级异常
**场景描述**：
- 内存不足
- 线程异常
- 系统资源耗尽

**处理策略**：
```cpp
// 全局异常捕获
try {
    // 业务逻辑
} catch (const std::exception& e) {
    ROS_ERROR_THROTTLE(1.0, "Exception in run loop: %s", e.what());
    // 继续运行，不退出循环
} catch (...) {
    ROS_ERROR_THROTTLE(1.0, "Unknown exception in run loop");
    // 继续运行，不退出循环
}
```

## 4. 关键数据结构设计

### 4.1 DebugStats结构体
```cpp
struct DebugStats {
    uint64_t total_packets_received = 0;  // 总接收数据包数量
    uint64_t valid_packets = 0;           // 有效数据包数量
    uint64_t invalid_packets = 0;         // 无效数据包数量
    uint64_t serial_errors = 0;           // 串口通信错误次数
    uint64_t publish_errors = 0;          // 消息发布错误次数
    double last_packet_time = 0.0;        // 最后一个数据包的时间戳
};
```

### 4.2 消息标识常量
```cpp
static constexpr std::uint8_t MESSAGE_HEADER = 0xA5;  // 消息头
static constexpr std::uint8_t MESSAGE_FOOTER = 0x0D;  // 消息尾
static constexpr size_t MESSAGE_LENGTH = 36;           // 消息长度
```

### 4.3 数据包格式
```
字节位置 | 字段名称 | 数据类型 | 说明
---------|----------|----------|------
0        | 消息头   | uint8    | 固定值 0xA5
1-4      | 时间戳   | float    | 传感器时间戳
5-8      | Fx       | float    | X方向力
9-12     | Fy       | float    | Y方向力
13-16    | Fz       | float    | Z方向力
17-20    | Mx       | float    | X方向力矩
21-24    | My       | float    | Y方向力矩
25-28    | Mz       | float    | Z方向力矩
29-32    | 温度     | float    | 传感器温度
33-34    | 保留     | uint16   | 保留字段
35       | 消息尾   | uint8    | 固定值 0x0D
```

## 5. 服务接口设计

### 5.1 GetDebugStats服务
**服务名称**: `/get_debug_stats`

**请求**: 空（无需参数）

**响应**:
```srv
uint64 total_packets_received  # 总接收数据包数量
uint64 valid_packets           # 有效数据包数量
uint64 invalid_packets         # 无效数据包数量
uint64 serial_errors           # 串口通信错误次数
uint64 publish_errors          # 消息发布错误次数
float64 last_packet_time       # 最后一个数据包的时间戳
float64 success_rate           # 数据包处理成功率
string status_message          # 状态汇总消息
```

**使用方式**:
```bash
# 命令行调用
rosservice call /get_debug_stats

# Python脚本调用
rosrun ft_sensor_node get_debug_stats.py
```

### 5.2 发布话题
**话题名称**: `/ft_sensor`（可配置）

**消息类型**: `geometry_msgs/WrenchStamped`

**发布频率**: 1000Hz（可配置）

## 6. 性能考虑

### 6.1 实时性优化
**高精度时钟**:
```cpp
auto start_time = std::chrono::high_resolution_clock::now();
auto elapsed = std::chrono::high_resolution_clock::now() - start_time;
```

**精确频率控制**:
```cpp
const auto period = std::chrono::microseconds(1000000 / sampling_frequency_);
auto sleep_time = period - elapsed;
if (sleep_time > std::chrono::microseconds(0)) {
    std::this_thread::sleep_for(sleep_time);
}
```

### 6.2 内存优化
**栈上缓冲区**:
```cpp
std::uint8_t buffer[MESSAGE_LENGTH];  // 避免动态内存分配
```

**内联函数**:
```cpp
inline float bytesToFloat(const std::uint8_t* bytes) {
    // 减少函数调用开销
}
```

**直接内存拷贝**:
```cpp
std::memcpy(&converter.bytes[0], bytes, 4);  // 比逐字节赋值更快
```

### 6.3 编译器优化
**分支预测优化**:
```cpp
if (__builtin_expect(!serial_port_ || !serial_port_->isOpen(), 0)) {
    // 异常分支
}
```

**likely分支优化**:
```cpp
if (__builtin_expect(bytes_read != MESSAGE_LENGTH, 0)) {
    // 异常分支
}
```

### 6.4 线程安全
**原子操作**:
```cpp
std::atomic<bool> running_;  // 线程安全的运行标志
```

**互斥锁**:
```cpp
std::mutex data_mutex_;  // 保护共享数据
```

## 7. 运维设计

### 7.1 配置管理
**配置文件**: `config/ft_sensor_node.yaml`
```yaml
ft_sensor_node:
  # 串口配置
  serial_port: "/dev/ttyUSB0"
  baud_rate: 921600
  data_bits: 8
  stop_bits: 1
  parity: 0
  timeout: 500
  
  # 采样配置
  sampling_frequency: 1000
  message_size: 36
  
  # 话题配置
  topic_name: "/ft_sensor"
  
  # 调试配置
  debug_mode: false
```

### 7.2 启动脚本
**启动文件**: `launch/ft_sensor_node.launch`
```xml
<launch>
  <arg name="ft_sensor_port" default="/dev/ttyUSB0" />
  <rosparam command="load" file="$(find ft_sensor_node)/config/ft_sensor_node.yaml" />
  <param name="ft_sensor_node/serial_port" value="$(arg ft_sensor_port)" />
  <node name="ft_sensor_node" pkg="ft_sensor_node" type="ft_sensor_node" output="screen">
    <param name="serial_port" value="$(arg ft_sensor_port)" />
  </node>
</launch>
```

### 7.3 监控和调试
**调试统计服务**:
- 实时获取系统运行状态
- 监控数据包处理情况
- 跟踪错误发生频率

**日志管理**:
- 使用ROS日志系统
- 分级日志输出（INFO, WARN, ERROR, DEBUG）
- 使用THROTTLE避免日志刷屏

**测试工具**:
- Python测试脚本：`test/test_ft_sensor_node.py`
- 调试统计脚本：`scripts/get_debug_stats.py`

### 7.4 故障处理
**自动恢复机制**:
- 串口异常时继续尝试读取
- 发布失败时继续处理数据
- 全局异常捕获防止节点崩溃

**错误分类统计**:
- 串口错误统计
- 数据包错误统计
- 发布错误统计

**健康检查**:
- 定期检查串口状态
- 监控数据包接收频率
- 验证发布器状态

### 7.5 部署考虑
**依赖管理**:
```xml
<build_depend>roscpp</build_depend>
<build_depend>std_msgs</build_depend>
<build_depend>geometry_msgs</build_depend>
<build_depend>realtime_tools</build_depend>
```

**编译配置**:
```cmake
find_package(catkin REQUIRED COMPONENTS
  roscpp std_msgs geometry_msgs message_generation realtime_tools
)
```

**权限要求**:
- 串口设备访问权限
- ROS网络通信权限
- 文件系统读写权限

## 8. 总结

FT Sensor Node设计充分考虑了实时性、可靠性和可维护性：

1. **实时性**: 高精度时钟控制、内联函数优化、栈上内存分配
2. **可靠性**: 全面的异常处理、自动恢复机制、健康检查
3. **可维护性**: 详细的调试统计、分级日志、模块化设计
4. **可扩展性**: 配置化参数、服务化接口、标准化消息格式

该设计能够满足工业级六维力传感器数据采集的严格要求。 