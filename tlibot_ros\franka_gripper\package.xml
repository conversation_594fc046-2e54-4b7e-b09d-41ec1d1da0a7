<?xml version="1.0"?>
<package format="2">
  <name>franka_gripper</name>
  <version>0.10.1</version>
  <description>This package implements the franka gripper of type Frank<PERSON> Hand for the use in ros</description>
  <maintainer email="<EMAIL>">Franka Emika GmbH</maintainer>
  <license>Apache 2.0</license>

  <url type="website">http://wiki.ros.org/franka_gripper</url>
  <url type="repository">https://github.com/frankaemika/franka_ros</url>
  <url type="bugtracker">https://github.com/frankaemika/franka_ros/issues</url>
  <author>Franka Emika GmbH</author>

  <buildtool_depend>catkin</buildtool_depend>

  <depend>roscpp</depend>
  <depend>message_generation</depend>
  <depend>libfranka</depend>
  <depend>control_msgs</depend>
  <depend>actionlib</depend>
  <depend>sensor_msgs</depend>
  <depend>xmlrpcpp</depend>
  <depend>actionlib_msgs</depend>
  <depend>libmodbus-dev</depend>

  <exec_depend>message_runtime</exec_depend>

  <build_export_depend>message_runtime</build_export_depend>
</package>
