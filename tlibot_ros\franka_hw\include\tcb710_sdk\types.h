#pragma once

#include <vector>
#include <string>
#include <array>
#include <map>
#include <memory>
#include <functional>

namespace tcb710_sdk {

// 基础类型定义
using JointAngles = std::array<double, 7>;
using Trajectory = std::vector<JointAngles>;
using StatusMap = std::map<std::string, std::string>;

// 坐标系类型
enum class CoordinateType {
    JOINT = 0,  // 关节坐标
    CART = 1,   // 直角坐标
    TOOL = 2,   // 工具坐标
    USER = 3    // 用户坐标
};

// 运动类型
enum class MovementType {
    MOVJ,   // 关节运动
    MOVL,   // 直线运动
    MOVC,   // 圆弧运动
    MOVS    // 样条曲线运动
};

// 日志级别
enum class LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARNING = 2,
    ERROR = 3
};

// 机器人状态
struct RobotStatus {
    bool is_connected = false;
    bool is_enabled = false;
    bool is_moving = false;
    bool has_error = false;
    JointAngles current_position;
    double current_speed = 0.0;
};

// 网络配置
struct NetworkConfig {
    std::string ip = "*************";
    int port_6001 = 6001;
    int port_7000 = 7000;
    int timeout_ms = 5000;
    int retry_count = 3;
};

// 运动参数
struct MovementParams {
    double speed = 100.0;
    double acceleration = 100.0;
    double deceleration = 100.0;
    int pl = 0;  // 平滑度参数
};

// 回调函数类型
using StatusCallback = std::function<void(const RobotStatus&)>;
using ErrorCallback = std::function<void(const std::string&)>;

} // namespace tcb710_sdk
