#pragma once

#include <controller_interface/multi_interface_controller.h>
#include <franka_hw/franka_cartesian_state_interface.h>
#include <franka_hw/franka_state_interface.h>
#include <ros/ros.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/TwistStamped.h>
#include <geometry_msgs/AccelStamped.h>

namespace franka_example_controllers {

/**
 * Test controller for CartesianStateInterface functionality.
 * This controller reads Cartesian state information and publishes it to ROS topics.
 */
class CartesianStateTestController : public controller_interface::MultiInterfaceController<
                                         franka_hw::CartesianStateInterface,
                                         franka_hw::FrankaStateInterface> {
 public:
  bool init(hardware_interface::RobotHW* robot_hardware, ros::NodeHandle& node_handle) override;
  void starting(const ros::Time&) override;
  void update(const ros::Time&, const ros::Duration& period) override;
  void stopping(const ros::Time&) override;

 private:
  franka_hw::CartesianStateInterface* cartesian_state_interface_;
  std::unique_ptr<franka_hw::CartesianStateHandle> cartesian_state_handle_;
  
  // ROS publishers for testing
  ros::Publisher pose_pub_;
  ros::Publisher velocity_pub_;
  ros::Publisher acceleration_pub_;
  
  // Test parameters
  double publish_rate_;
  ros::Time last_publish_time_;
};

}  // namespace franka_example_controllers
