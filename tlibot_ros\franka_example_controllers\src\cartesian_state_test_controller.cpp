#include <franka_example_controllers/cartesian_state_test_controller.h>
#include <pluginlib/class_list_macros.h>
#include <tf2/LinearMath/Matrix3x3.h>
#include <tf2/LinearMath/Quaternion.h>

namespace franka_example_controllers {

bool CartesianStateTestController::init(hardware_interface::RobotHW* robot_hardware,
                                        ros::NodeHandle& node_handle) {
  // Get CartesianStateInterface
  cartesian_state_interface_ = robot_hardware->get<franka_hw::CartesianStateInterface>();
  if (cartesian_state_interface_ == nullptr) {
    ROS_ERROR("CartesianStateTestController: Could not get Cartesian State interface from hardware");
    return false;
  }

  // Get arm_id parameter
  std::string arm_id;
  if (!node_handle.getParam("arm_id", arm_id)) {
    ROS_ERROR("CartesianStateTestController: Could not get parameter arm_id");
    return false;
  }

  // Get CartesianStateHandle
  try {
    cartesian_state_handle_ = std::make_unique<franka_hw::CartesianStateHandle>(
        cartesian_state_interface_->getHandle(arm_id + "_cartesian_state"));
  } catch (const hardware_interface::HardwareInterfaceException& e) {
    ROS_ERROR_STREAM("CartesianStateTestController: Exception getting Cartesian state handle: " << e.what());
    return false;
  }

  // Get FrankaStateInterface for comparison
  auto state_interface = robot_hardware->get<franka_hw::FrankaStateInterface>();
  if (state_interface == nullptr) {
    ROS_ERROR("CartesianStateTestController: Could not get state interface from hardware");
    return false;
  }

  // Initialize publishers
  pose_pub_ = node_handle.advertise<geometry_msgs::PoseStamped>("cartesian_pose", 1);
  velocity_pub_ = node_handle.advertise<geometry_msgs::TwistStamped>("cartesian_velocity", 1);
  acceleration_pub_ = node_handle.advertise<geometry_msgs::AccelStamped>("cartesian_acceleration", 1);

  // Get publish rate parameter
  node_handle.param("publish_rate", publish_rate_, 10.0);  // Default 10 Hz

  ROS_INFO("CartesianStateTestController: Initialized successfully");
  return true;
}

void CartesianStateTestController::starting(const ros::Time& time) {
  last_publish_time_ = time;
  ROS_INFO("CartesianStateTestController: Started");
}

void CartesianStateTestController::update(const ros::Time& time, const ros::Duration& period) {
  // Check if it's time to publish
  if ((time - last_publish_time_).toSec() < (1.0 / publish_rate_)) {
    return;
  }

  try {
    // Get Cartesian state data
    const auto& pose = cartesian_state_handle_->getPose();
    const auto& velocity = cartesian_state_handle_->getVelocity();
    const auto& acceleration = cartesian_state_handle_->getAcceleration();

    // Publish pose
    geometry_msgs::PoseStamped pose_msg;
    pose_msg.header.stamp = time;
    pose_msg.header.frame_id = "base_link";
    
    // Extract position from transformation matrix (pose is 4x4 matrix in column-major order)
    pose_msg.pose.position.x = pose[12];  // Translation X
    pose_msg.pose.position.y = pose[13];  // Translation Y
    pose_msg.pose.position.z = pose[14];  // Translation Z
    
    // Extract rotation matrix and convert to quaternion
    tf2::Matrix3x3 rotation_matrix(
        pose[0], pose[4], pose[8],
        pose[1], pose[5], pose[9],
        pose[2], pose[6], pose[10]
    );
    tf2::Quaternion quaternion;
    rotation_matrix.getRotation(quaternion);
    
    pose_msg.pose.orientation.x = quaternion.x();
    pose_msg.pose.orientation.y = quaternion.y();
    pose_msg.pose.orientation.z = quaternion.z();
    pose_msg.pose.orientation.w = quaternion.w();
    
    pose_pub_.publish(pose_msg);

    // Publish velocity
    geometry_msgs::TwistStamped velocity_msg;
    velocity_msg.header.stamp = time;
    velocity_msg.header.frame_id = "base_link";
    velocity_msg.twist.linear.x = velocity[0];
    velocity_msg.twist.linear.y = velocity[1];
    velocity_msg.twist.linear.z = velocity[2];
    velocity_msg.twist.angular.x = velocity[3];
    velocity_msg.twist.angular.y = velocity[4];
    velocity_msg.twist.angular.z = velocity[5];
    
    velocity_pub_.publish(velocity_msg);

    // Publish acceleration
    geometry_msgs::AccelStamped acceleration_msg;
    acceleration_msg.header.stamp = time;
    acceleration_msg.header.frame_id = "base_link";
    acceleration_msg.accel.linear.x = acceleration[0];
    acceleration_msg.accel.linear.y = acceleration[1];
    acceleration_msg.accel.linear.z = acceleration[2];
    acceleration_msg.accel.angular.x = acceleration[3];
    acceleration_msg.accel.angular.y = acceleration[4];
    acceleration_msg.accel.angular.z = acceleration[5];
    
    acceleration_pub_.publish(acceleration_msg);

    // Log data for debugging
    ROS_DEBUG_THROTTLE(1.0, "CartesianStateTestController: Pose: [%.3f, %.3f, %.3f], Vel: [%.3f, %.3f, %.3f]",
                       pose_msg.pose.position.x, pose_msg.pose.position.y, pose_msg.pose.position.z,
                       velocity[0], velocity[1], velocity[2]);

    last_publish_time_ = time;

  } catch (const std::exception& e) {
    ROS_ERROR_STREAM("CartesianStateTestController: Error in update: " << e.what());
  }
}

void CartesianStateTestController::stopping(const ros::Time&) {
  ROS_INFO("CartesianStateTestController: Stopped");
}

}  // namespace franka_example_controllers

PLUGINLIB_EXPORT_CLASS(franka_example_controllers::CartesianStateTestController,
                       controller_interface::ControllerBase)
