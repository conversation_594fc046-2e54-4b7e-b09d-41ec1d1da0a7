<?xml version="1.0" encoding="utf-8"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="hand">
  <!-- safety_distance: Minimum safety distance in [m] by which the collision volumes are expanded and which is enforced during robot motions -->
  <xacro:macro name="franka_hand" params="connected_to:='' arm_id:='panda' rpy:='0 0 0' xyz:='0 0 0' tcp_xyz:='0 0 0.1034' tcp_rpy:='0 0 0' safety_distance:=0 gazebo:=false description_pkg:=franka_description">
    <xacro:unless value="${connected_to == ''}">
      <joint name="${arm_id}_hand_joint" type="fixed">
        <parent link="${connected_to}" />
        <child link="${arm_id}_hand" />
        <origin xyz="${xyz}" rpy="${rpy}" />
      </joint>
    </xacro:unless>

    <xacro:link_with_sc name="hand" gazebo="${gazebo}">
      <self_collision_geometries>
        <xacro:collision_capsule xyz="0 0 0.04" direction="y" radius="${0.04+safety_distance}" length="0.1" />
        <xacro:collision_capsule xyz="0 0 0.10" direction="y" radius="${0.02+safety_distance}" length="0.1" />
      </self_collision_geometries>
    </xacro:link_with_sc>

    <!-- Define the hand_tcp frame -->
    <link name="${arm_id}_hand_tcp" />
    <joint name="${arm_id}_hand_tcp_joint" type="fixed">
      <origin xyz="${tcp_xyz}" rpy="${tcp_rpy}" />
      <parent link="${arm_id}_hand" />
      <child link="${arm_id}_hand_tcp" />
    </joint>
    <link name="${arm_id}_leftfinger">
      <visual>
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/visual/finger.dae" />
        </geometry>
      </visual>
      <!-- screw mount -->
      <collision>
        <origin xyz="0 18.5e-3 11e-3" rpy="0 0 0" />
        <geometry>
          <box size="22e-3 15e-3 20e-3" />
        </geometry>
      </collision>
      <!-- cartriage sledge -->
      <collision>
        <origin xyz="0 6.8e-3 2.2e-3" rpy="0 0 0" />
        <geometry>
          <box size="22e-3 8.8e-3 3.8e-3" />
        </geometry>
      </collision>
      <!-- diagonal finger -->
      <collision>
        <origin xyz="0 15.9e-3 28.35e-3" rpy="${pi/6} 0 0" />
        <geometry>
          <box size="17.5e-3 7e-3 23.5e-3" />
        </geometry>
      </collision>
      <!-- rubber tip with which to grasp -->
      <collision>
        <origin xyz="0 7.58e-3 45.25e-3" rpy="0 0 0" />
        <geometry>
          <box size="17.5e-3 15.2e-3 18.5e-3" />
        </geometry>
      </collision>
      <xacro:if value="${gazebo}">
        <xacro:inertial_props name="leftfinger" />
      </xacro:if>
    </link>
    <link name="${arm_id}_rightfinger">
      <visual>
        <origin xyz="0 0 0" rpy="0 0 ${pi}" />
        <geometry>
          <mesh filename="package://${description_pkg}/meshes/visual/finger.dae" />
        </geometry>
      </visual>
      <!-- screw mount -->
      <collision>
        <origin xyz="0 -18.5e-3 11e-3" rpy="0 0 0" />
        <geometry>
          <box size="22e-3 15e-3 20e-3" />
        </geometry>
      </collision>
      <!-- cartriage sledge -->
      <collision>
        <origin xyz="0 -6.8e-3 2.2e-3" rpy="0 0 0" />
        <geometry>
          <box size="22e-3 8.8e-3 3.8e-3" />
        </geometry>
      </collision>
      <!-- diagonal finger -->
      <collision>
        <origin xyz="0 -15.9e-3 28.35e-3" rpy="${-pi/6} 0 0" />
        <geometry>
          <box size="17.5e-3 7e-3 23.5e-3" />
        </geometry>
      </collision>
      <!-- rubber tip with which to grasp -->
      <collision>
        <origin xyz="0 -7.58e-3 45.25e-3" rpy="0 0 0" />
        <geometry>
          <box size="17.5e-3 15.2e-3 18.5e-3" />
        </geometry>
      </collision>
      <xacro:if value="${gazebo}">
        <xacro:inertial_props name="rightfinger" />
      </xacro:if>
    </link>
    <joint name="${arm_id}_finger_joint1" type="prismatic">
      <parent link="${arm_id}_hand" />
      <child link="${arm_id}_leftfinger" />
      <origin xyz="0 0 0.0584" rpy="0 0 0" />
      <axis xyz="0 1 0" />
      <limit effort="100" lower="0.0" upper="0.04" velocity="0.2" />
      <dynamics damping="0.3" />
    </joint>
    <joint name="${arm_id}_finger_joint2" type="prismatic">
      <parent link="${arm_id}_hand" />
      <child link="${arm_id}_rightfinger" />
      <origin xyz="0 0 0.0584" rpy="0 0 0" />
      <axis xyz="0 -1 0" />
      <limit effort="100" lower="0.0" upper="0.04" velocity="0.2" />
      <mimic joint="${arm_id}_finger_joint1" />
      <dynamics damping="0.3" />
    </joint>
  </xacro:macro>
</robot>
