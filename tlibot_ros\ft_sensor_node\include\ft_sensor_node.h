#ifndef FT_SENSOR_NODE_H
#define FT_SENSOR_NODE_H

#include <ros/ros.h>
#include <geometry_msgs/WrenchStamped.h>
#include <realtime_tools/realtime_publisher.h>
#include <realtime_tools/realtime_buffer.h>
#include <ft_sensor_node/GetDebugStats.h>
#include <serial/serial.h>
#include <cstring>
#include <thread>
#include <atomic>
#include <mutex>
#include <chrono>

namespace ft_sensor {

// 六维力传感器节点类
class FTSensorNode {
public:
    FTSensorNode();
    ~FTSensorNode();

    // 初始化节点
    bool initialize();
    
    // 启动节点
    void start();
    
    // 停止节点
    void stop();
    
    // 运行节点主循环
    void run();

private:
    // 读取串口数据
    bool readSerialData();
    
    // 处理有效数据包 - 内联函数减少调用开销
    inline bool processValidPacket(const std::uint8_t* buffer) {
        // 解析传感器数据并发布 - 直接赋值，减少中间变量
        // 发布传感器数据
        if (wrench_pub_ && wrench_pub_->trylock()) {
            // 获取消息引用
            geometry_msgs::WrenchStamped& wrench_msg = wrench_pub_->msg_;
            
            // 设置时间戳 - 使用串口数据包中的时间戳
            wrench_msg.header.stamp = ros::Time(bytesToFloat(&buffer[1]));
            wrench_msg.header.frame_id = "ft_sensor_frame";
            
            // 设置力数据 (通道1-3) - 直接赋值
            wrench_msg.wrench.force.x = bytesToFloat(&buffer[5]);   // Fx
            wrench_msg.wrench.force.y = bytesToFloat(&buffer[9]);   // Fy
            wrench_msg.wrench.force.z = bytesToFloat(&buffer[13]);  // Fz
            
            // 设置力矩数据 (通道4-6) - 直接赋值
            wrench_msg.wrench.torque.x = bytesToFloat(&buffer[17]); // Mx
            wrench_msg.wrench.torque.y = bytesToFloat(&buffer[21]); // My
            wrench_msg.wrench.torque.z = bytesToFloat(&buffer[25]); // Mz
            
            // 解锁并发布消息
            wrench_pub_->unlockAndPublish();
            
            // 更新调试统计
            debug_stats_.total_packets_received++;
            debug_stats_.valid_packets++;
            debug_stats_.last_packet_time = wrench_msg.header.stamp.toSec();
            
            ROS_DEBUG("Published realtime wrench message - Timestamp: %.6f, Frame: %s, Force: [%.3f, %.3f, %.3f], Torque: [%.3f, %.3f, %.3f], Temp: %.2f",
                    wrench_msg.header.stamp.toSec(), wrench_msg.header.frame_id.c_str(),
                    wrench_msg.wrench.force.x, wrench_msg.wrench.force.y, wrench_msg.wrench.force.z,
                    wrench_msg.wrench.torque.x, wrench_msg.wrench.torque.y, wrench_msg.wrench.torque.z,
                    bytesToFloat(&buffer[29]));
            
            return true;
        } else {
            debug_stats_.publish_errors++;
            return false;
        }
    }
    
    // 调试统计服务回调函数
    bool getDebugStats(ft_sensor_node::GetDebugStats::Request& req, 
                      ft_sensor_node::GetDebugStats::Response& res);
    
    // 字节转浮点数 - 内联函数提高性能
    inline float bytesToFloat(const std::uint8_t* bytes) {
        float value;
        std::memcpy(&value, bytes, sizeof(float));
        return value;
    }
    


private:
    // ROS相关
    ros::NodeHandle nh_;
    ros::NodeHandle private_nh_;
    
    // 串口相关
    std::unique_ptr<serial::Serial> serial_port_;
    std::string serial_port_name_;
    int baud_rate_;
    int data_bits_;
    int stop_bits_;
    int parity_;
    int timeout_;
    
    // 配置参数
    int sampling_frequency_;
    int message_size_;
    std::string topic_name_;
    
    // 发布者
    std::unique_ptr<realtime_tools::RealtimePublisher<geometry_msgs::WrenchStamped>> wrench_pub_;
    
    // 调试统计服务
    ros::ServiceServer debug_stats_service_;
    
    // 调试统计
    struct DebugStats {
        uint64_t total_packets_received = 0;
        uint64_t valid_packets = 0;
        uint64_t invalid_packets = 0;
        uint64_t serial_errors = 0;
        uint64_t publish_errors = 0;
        double last_packet_time = 0.0;
    } debug_stats_;
    
    // 线程控制
    std::atomic<bool> running_;
    std::thread read_thread_;
    std::mutex data_mutex_;
    
    // 消息标识
    static constexpr std::uint8_t MESSAGE_HEADER = 0xA5;
    static constexpr std::uint8_t MESSAGE_FOOTER = 0x0D;
    static constexpr size_t MESSAGE_LENGTH = 36;
};

} // namespace ft_sensor

#endif // FT_SENSOR_NODE_H 