#!/usr/bin/env python
PACKAGE = "franka_example_controllers"

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

gen.add("desired_mass", double_t, 0, "desired mass for rendered force due to gravity applied in the z axis", 0.0,  0.0, 2.0)
gen.add("k_p", double_t, 0, "force P gain", 0.0,  0.0, 2.0)
gen.add("k_i", double_t, 0, "force I gain", 0.0,  0.0, 2.0)

exit(gen.generate(PACKAGE, "dynamic_mass", "desired_mass_param"))
