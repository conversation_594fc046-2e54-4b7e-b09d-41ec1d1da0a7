<?xml version="1.0"?>
<launch>
  <arg name="leader_ip" doc="left robot from user persepective standing in front of the robots for a robot setup according to $(find franka_description)/robots/dual_panda/dual_panda_example.urdf.xacro" />
  <arg name="follower_ip" doc="right robot from user persepective standing in front of the robots for a robot setup according to $(find franka_description)/robots/dual_panda/dual_panda_example.urdf.xacro" />

  <arg name="robot_id" default="panda_teleop" doc="name of the 2-arm robot" />
  <arg name="debug" default="false" doc="publish debug information and enable dynamic parameter tuning" />
  <arg name="rviz" default="true" doc="open rviz on launch to show the virtual robot setup" />
  <arg name="load_gripper" default="false" doc="start teleoperation for the grippers (full open/close is mirrored to follower)" />
  <arg name="gripper_homed" default="false" doc="is the gripper already homed? If not the gripper will perform an initial homing motion" />

  <remap from="dyn_reconf_teleop_param_node" to="$(arg robot_id)/dyn_reconf_teleop_param_node" />
  <remap from="$(arg robot_id)/dyn_reconf_teleop_param_node/leader_contact_force_threshold" to="$(arg robot_id)/teleop_joint_pd_example_controller/leader/contact_force_threshold" />
  <remap from="$(arg robot_id)/dyn_reconf_teleop_param_node/follower_contact_force_threshold" to="$(arg robot_id)/teleop_joint_pd_example_controller/follower/contact_force_threshold" />

  <include file="$(find franka_control)/launch/franka_combined_control.launch">
    <arg name="robot_id" value="$(arg robot_id)" />
    <arg name="robot_ips" value="{leader/robot_ip: $(arg leader_ip), follower/robot_ip: $(arg follower_ip)}" />
    <arg name="hw_config_file" value="$(find franka_example_controllers)/config/teleop_joint_pd_example_control_node.yaml" />
    <arg name="robot" value="$(find franka_description)/robots/dual_panda/dual_panda_example.urdf.xacro" />
    <arg name="args" value="arm_id_1:=leader arm_id_2:=follower" />
    <arg name="controllers_file" value="$(find franka_example_controllers)/config/teleop_state_controllers.yaml" />
    <arg name="controllers_to_start" value="leader_state_controller follower_state_controller" />
    <arg name="joint_states_source_list" value="[leader_state_controller/joint_states, follower_state_controller/joint_states, leader/franka_gripper/joint_states, follower/franka_gripper/joint_states]" />
  </include>

  <group ns="$(arg robot_id)">
    <include file="$(find franka_example_controllers)/launch/teleop_gripper.launch" if="$(arg load_gripper)">
      <arg name="leader_ip" value="$(arg leader_ip)" />
      <arg name="follower_ip" value="$(arg follower_ip)" />
      <arg name="leader_id" value="leader" />
      <arg name="follower_id" value="follower" />
      <arg name="gripper_homed" value="$(arg gripper_homed)" />
    </include>

    <rosparam command="load" file="$(find franka_example_controllers)/config/franka_example_controllers.yaml" />
    <rosparam param="teleop_joint_pd_example_controller/debug" subst_value="true">$(arg debug)</rosparam>
    <node name="controller_spawner" pkg="controller_manager" type="spawner" respawn="false" output="screen" args="teleop_joint_pd_example_controller" />

    <node name="rqt_reconfigure" pkg="rqt_reconfigure" type="rqt_reconfigure" required="false" if="$(eval arg('debug') or arg('load_gripper'))" />

    <node pkg="rviz" type="rviz" output="screen" name="rviz" args="-d $(find franka_example_controllers)/launch/rviz/teleop_joint_pd_example.rviz" if="$(arg rviz)" />
  </group>
</launch>