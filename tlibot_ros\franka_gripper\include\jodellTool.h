int* getComPort();
int serialOperation(int comIndex, int portNo, int baudRate, bool status);
int* getStatus(int comIndex, int slaveId, int address, int readMode, int count);
int writeDataToRegister(int comIndex, int slaveId, int address, int* sendCmdBuf, int count);
int clawEnable(int comIndex, int slaveId, bool status);
int rotateEnable(int comIndex, int slaveId, bool status);
int runWithoutParam(int comIndex, int slaveId, int cmdId);
int rotateWithoutParam(int comIndex, int slaveId, int cmdId);
int runWithParam(int comIndex, int slaveId, int pos, int speed, int torque);
int runWithParamOfErg32(int comIndex, int slaveId, int pos, int speed, int torque);
int rotateWithParam(int comIndex, int slaveId, int angle, int speed, int torque, bool absStatus, int cycleNum);
int changeSalveId(int comIndex, int oldId, int newId);
int changeSalveIdOfErg32(int comIndex, int oldId, int newId);
int changeBaudRate(int comIndex, int slaveId, int baudRate);
int changeBaudRateOfErg32(int comIndex, int slaveId, int baudRate);
int clawEncoderZero(int comIndex, int slaveId);
int switchMode(int comIndex, int slaveId, int modeIndex);
int getClawCurrentStatus(int comIndex, int slaveId);
int getClawCurrentStatusOfErg32(int comIndex, int slaveId);
int getRotateCurrentStatusOfErg32(int comIndex, int slaveId);
int getClawCurrentLocation(int comIndex, int slaveId);
int getClawCurrentLocationOfErg32(int comIndex, int slaveId);
int getCurrentAbsAngleOfErg32(int comIndex, int slaveId);
int getCurrentRelAngleOfErg32(int comIndex, int slaveId);
int getClawCurrentSpeed(int comIndex, int slaveId);
int getClawCurrentSpeedOfErg32(int comIndex, int slaveId);
int getRotateCurrentSpeedOfErg32(int comIndex, int slaveId);
int getClawCurrentTorque(int comIndex, int slaveId);
int getClawCurrentTorqueOfErg32(int comIndex, int slaveId);
int getRotateCurrentTorqueOfErg32(int comIndex, int slaveId);
int getClawCurrentTemperature(int comIndex, int slaveId);
int getClawCurrentTemperatureOfErg32(int comIndex, int slaveId);
int getClawCurrentVoltage(int comIndex, int slaveId);
int getClawCurrentVoltageOfErg32(int comIndex, int slaveId);
int* querySoftwareVersion(int comIndex, int slaveId);
int* querySoftwareVersionOfErg32(int comIndex, int slaveId);

int enableDeviceOfEvs(int comIndex, int slaveId, int state);
int startOrStopDeviceOfEvs(int comIndex, int slaveId, int mode, int runFlag, int breakState);
int startOrStopDeviceOfEvs2(int comIndex, int slaveId, int mode, int runFlag, int channelIndex, int breakState);
int switchModeOfEvs(int comIndex, int slaveId, int modeIndex);
int writeCustomParamOfEvs(int comIndex, int slaveId, int maxPressure, int minPressure, int timeout);
int writeCustomParamOfEvs2(int comIndex, int slaveId, int channelNo, int maxPressure, int minPressure, int timeout);
int getDeviceCurrentStateOfEvs(int comIndex, int slaveId);
int getDeviceCurrentStateOfEvs2(int comIndex, int slaveId, int channelNo);
int getDeviceCurrentVacuumDegreeOfEvs(int comIndex, int slaveId);
int getDeviceCurrentVacuumDegreeOfEvs2(int comIndex, int slaveId, int channelNo);
int getDeviceCurrentTemperatureOfEvs(int comIndex, int slaveId);
int getDeviceCurrentVoltageOfEvs(int comIndex, int slaveId);

int enableCollisionOfLepg(int comIndex, int slaveId, bool state);
int setCollisionThresholdOfLepg_1(int comIndex, int slaveId, int value);
int setCollisionThresholdOfLepg_2(int comIndex, int slaveId, int value);
int setCollisionThresholdOfLepg_3(int comIndex, int slaveId, int value);
int setDropThresholdOfLepg(int comIndex, int slaveId, int value);
int searchRangeOfLepg(int comIndex, int slaveId);
int manualSavePositionOfLepg(int comIndex, int slaveId);
int setPreImpactOfLepg(int comIndex, int slaveId, int value);
int setPreImpactSpeedOfLepg(int comIndex, int slaveId, int value);

int restartDeviceOfEls(int comIndex, int slaveId);
int saveParamOfEls(int comIndex, int slaveId);
int restoreDefaultOfEls(int comIndex, int slaveId);
int chooseSignalSourceOfEls(int comIndex, int slaveId, int value);
int chooseUserControlModeOfEls(int comIndex, int slaveId, int value);
int enableDeviceOfEls(int comIndex, int slaveId, int value);
int resetDeviceOfEls(int comIndex, int slaveId);
int controlActionOfEls(int comIndex, int slaveId, int value);
int chooseMotorDirdctionOfEls(int comIndex, int slaveId, int value);
int setPointPositionOfEls(int comIndex, int slaveId, int pointNo, int value);
int readPointPositionOfEls(int comIndex, int slaveId, int pointNo);
int setPointSpeedOfEls(int comIndex, int slaveId, int pointNo, int value);
int readPointSpeedOfEls(int comIndex, int slaveId, int pointNo);
int setPointTorqueOfEls(int comIndex, int slaveId, int pointNo, int value);
int readPointTorqueOfEls(int comIndex, int slaveId, int pointNo);
int setRelativeMotionDistanceOfEls(int comIndex, int slaveId, int value);
int readRelativeMotionDistanceOfEls(int comIndex, int slaveId);
int setJogStepValueOfEls(int comIndex, int slaveId, int value);
int readJogStepValueOfEls(int comIndex, int slaveId);
int setDecelerationPercentageOfPositionOfEls(int comIndex, int slaveId, int value);
int readDecelerationPercentageOfPositionOfEls(int comIndex, int slaveId);
int setDecelerationPercentageOfSpeedOfEls(int comIndex, int slaveId, int value);
int readDecelerationPercentageOfSpeedOfEls(int comIndex, int slaveId);
int setDecelerationDirectionOfEls(int comIndex, int slaveId, int value);
int readDecelerationDirectionOfEls(int comIndex, int slaveId);
int setIoForcedOutputOfEls(int comIndex, int slaveId, int value);
int readIoForcedOutputOfEls(int comIndex, int slaveId);
int forcedControlBrakeOfEls(int comIndex, int slaveId, int value);
int changeSlaveIdOfEls(int comIndex, int slaveId, int value);
int changeBaudRateOfEls(int comIndex, int slaveId, int value);
int getDeviceRunStateOfEls(int comIndex, int slaveId);
int getDeviceActionStateOfEls(int comIndex, int slaveId);
int getDeciveCurrentLocationOfEls(int comIndex, int slaveId);
int getDeciveCurrentSpeedOfEls(int comIndex, int slaveId);
int getDeviceCurrentTorqueOfEls(int comIndex, int slaveId);
int getDeviceErrorStateOfEls(int comIndex, int slaveId);
int getMotorErrorStateOfEls(int comIndex, int slaveId);
int getIoInputMonitoringOfEls(int comIndex, int slaveId);
int getIoOutputMonitoringOfEls(int comIndex, int slaveId);