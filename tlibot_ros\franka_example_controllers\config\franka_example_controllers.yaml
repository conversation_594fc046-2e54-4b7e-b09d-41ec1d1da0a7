joint_velocity_example_controller:
    type: franka_example_controllers/JointVelocityExampleController
    arm_id: $(arg arm_id)
    joint_names:
        - $(arg arm_id)_joint1
        - $(arg arm_id)_joint2
        - $(arg arm_id)_joint3
        - $(arg arm_id)_joint4
        - $(arg arm_id)_joint5
        - $(arg arm_id)_joint6
        - $(arg arm_id)_joint7

joint_position_example_controller:
    type: franka_example_controllers/JointPositionExampleController
    joint_names:
        - $(arg arm_id)_joint1
        - $(arg arm_id)_joint2
        - $(arg arm_id)_joint3
        - $(arg arm_id)_joint4
        - $(arg arm_id)_joint5
        - $(arg arm_id)_joint6
        - $(arg arm_id)_joint7

cartesian_pose_example_controller:
    type: franka_example_controllers/CartesianPoseExampleController
    arm_id: $(arg arm_id)

elbow_example_controller:
    type: franka_example_controllers/ElbowExampleController
    arm_id: $(arg arm_id)

cartesian_velocity_example_controller:
    type: franka_example_controllers/CartesianVelocityExampleController
    arm_id: $(arg arm_id)

model_example_controller:
    type: franka_example_controllers/ModelExampleController
    arm_id: $(arg arm_id)

force_example_controller:
    type: franka_example_controllers/ForceExampleController
    arm_id: $(arg arm_id)
    joint_names:
        - $(arg arm_id)_joint1
        - $(arg arm_id)_joint2
        - $(arg arm_id)_joint3
        - $(arg arm_id)_joint4
        - $(arg arm_id)_joint5
        - $(arg arm_id)_joint6
        - $(arg arm_id)_joint7

joint_impedance_example_controller:
    type: franka_example_controllers/JointImpedanceExampleController
    arm_id: $(arg arm_id)
    joint_names:
        - $(arg arm_id)_joint1
        - $(arg arm_id)_joint2
        - $(arg arm_id)_joint3
        - $(arg arm_id)_joint4
        - $(arg arm_id)_joint5
        - $(arg arm_id)_joint6
        - $(arg arm_id)_joint7
    k_gains:
        - 600.0
        - 600.0
        - 600.0
        - 600.0
        - 250.0
        - 150.0
        - 50.0
    d_gains:
        - 50.0
        - 50.0
        - 50.0
        - 20.0
        - 20.0
        - 20.0
        - 10.0
    radius: 0.1
    acceleration_time: 2.0
    vel_max: 0.15
    publish_rate: 10.0
    coriolis_factor: 1.0

cartesian_impedance_example_controller:
    type: franka_example_controllers/CartesianImpedanceExampleController
    arm_id: $(arg arm_id)
    joint_names:
        - $(arg arm_id)_joint1
        - $(arg arm_id)_joint2
        - $(arg arm_id)_joint3
        - $(arg arm_id)_joint4
        - $(arg arm_id)_joint5
        - $(arg arm_id)_joint6
        - $(arg arm_id)_joint7

dual_arm_cartesian_impedance_example_controller:
    type: franka_example_controllers/DualArmCartesianImpedanceExampleController
    right:
        arm_id: panda_1
        joint_names:
            - panda_1_joint1
            - panda_1_joint2
            - panda_1_joint3
            - panda_1_joint4
            - panda_1_joint5
            - panda_1_joint6
            - panda_1_joint7
    left:
        arm_id: panda_2
        joint_names:
            - panda_2_joint1
            - panda_2_joint2
            - panda_2_joint3
            - panda_2_joint4
            - panda_2_joint5
            - panda_2_joint6
            - panda_2_joint7

teleop_joint_pd_example_controller:
   type: franka_example_controllers/TeleopJointPDExampleController
   leader:
       arm_id: leader
       joint_names:
            - leader_joint1
            - leader_joint2
            - leader_joint3
            - leader_joint4
            - leader_joint5
            - leader_joint6
            - leader_joint7
       # WARNING: Varying the following parameters may result in instability!
       d_gains_lower: [1.0, 1.0, 1.0, 1.0, 0.33, 0.33, 0.17]
       d_gains_upper: [35.0, 35.0, 35.0, 35.0, 10.0, 10.0, 8.0]
       dq_max_lower: [1.3, 1.3, 1.3, 1.3, 1.4, 1.4, 1.4] # [rad/s]
       dq_max_upper: [1.9, 1.9, 1.9, 1.9, 2.2, 2.2, 2.2] # [rad/s]
       contact_force_threshold: 4.0 # [N]

   follower:
       arm_id: follower
       joint_names:
            - follower_joint1
            - follower_joint2
            - follower_joint3
            - follower_joint4
            - follower_joint5
            - follower_joint6
            - follower_joint7
       # WARNING: Varying the following parameters may result in instability!
       p_gains: [900.0, 900.0, 900.0, 900.0, 375.0, 225.0, 100.0]
       d_gains: [45.0, 45.0, 45.0, 45.0, 15.0, 15.0, 10.0]
       drift_comp_gains: [4.3, 4.3, 4.3, 4.3, 4.3, 4.3, 4.3]
       dq_max_lower: [0.8, 0.8, 0.8, 0.8, 2.5, 2.5, 2.5] # [rad/s]
       dq_max_upper: [2.0, 2.0, 2.0, 2.0, 2.5, 2.5, 2.5] # [rad/s]
       ddq_max_lower: [0.8, 0.8, 0.8, 0.8, 10.0, 10.0, 10.0] # [rad/s^2]
       ddq_max_upper: [6.0, 6.0, 6.0, 6.0, 15.0, 20.0, 20.0] # [rad/s^2]
       contact_force_threshold: 5.0 # [N]
