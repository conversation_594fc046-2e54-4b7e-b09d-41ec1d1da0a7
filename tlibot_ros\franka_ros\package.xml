<?xml version="1.0"?>
<package format="2">
  <name>franka_ros</name>
  <version>0.10.1</version>
  <description>franka_ros is a metapackage for all Franka Emika ROS packages</description>
  <maintainer email="<EMAIL>">Franka Emika GmbH</maintainer>
  <license>Apache 2.0</license>

  <url type="website">http://wiki.ros.org/franka_ros</url>
  <url type="repository">https://github.com/frankaemika/franka_ros</url>
  <url type="bugtracker">https://github.com/frankaemika/franka_ros/issues</url>
  <author>Franka Emika GmbH</author>

  <buildtool_depend>catkin</buildtool_depend>

  <exec_depend>franka_control</exec_depend>
  <exec_depend>franka_description</exec_depend>
  <exec_depend>franka_example_controllers</exec_depend>
  <exec_depend>franka_gripper</exec_depend>
  <exec_depend>franka_hw</exec_depend>
  <exec_depend>franka_msgs</exec_depend>
  <!-- <exec_depend>franka_visualization</exec_depend>
  <exec_depend>franka_gazebo</exec_depend> -->

  <export>
    <metapackage />
  </export>
</package>
