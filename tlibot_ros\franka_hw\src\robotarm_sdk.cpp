#include "tcb710_sdk/robotarm_sdk.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <thread>
#include <algorithm>
#include <cstring>

#include <ros/console.h>

#ifdef _WIN32
bool tcb710_sdk::RobotArmSDK::wsa_initialized_ = false;
#endif

namespace tcb710_sdk {

RobotArmSDK::RobotArmSDK(const std::string& ip, int port, int log_level)
    : ip_(ip), port_(port), socket_(INVALID_SOCKET), connect_flag_(false) {
    
    // 设置日志级别
    if (log_level == 0) {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Info);
    } else if (log_level == 1) {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Debug);
    }
    
    // 验证端口号
    if (port != 6001 && port != 7000) {
        ROS_ERROR("RobotArmSDK: Invalid port number: %d. Must be 6001 or 7000.", port);
        return;
    }
    
    ROS_INFO("RobotArmSDK: Initializing TCB710 SDK, IP: %s, Port: %d", ip.c_str(), port);
    
    // 初始化socket系统
    if (!initializeSocket()) {
        ROS_ERROR("RobotArmSDK: Failed to initialize socket system");
    }
}

RobotArmSDK::~RobotArmSDK() {
    disconnect();
    cleanupSocket();
}

bool RobotArmSDK::initializeSocket() {
#ifdef _WIN32
    if (!wsa_initialized_) {
        WSADATA wsaData;
        int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
        if (result != 0) {
            ROS_ERROR("RobotArmSDK: WSAStartup failed: %d", result);
            return false;
        }
        wsa_initialized_ = true;
    }
#endif
    return true;
}

void RobotArmSDK::cleanupSocket() {
#ifdef _WIN32
    if (wsa_initialized_) {
        WSACleanup();
        wsa_initialized_ = false;
    }
#endif
}

bool RobotArmSDK::connect() {
    if (connect_flag_.load()) {
        ROS_WARN("RobotArmSDK: Already connected");
        return true;
    }
    
    try {
        // 创建socket
        socket_ = socket(AF_INET, SOCK_STREAM, 0);
        if (socket_ == INVALID_SOCKET) {
            ROS_ERROR("RobotArmSDK: Failed to create socket");
            return false;
        }
        
        // 设置地址
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(port_);
        
#ifdef _WIN32
        server_addr.sin_addr.s_addr = inet_addr(ip_.c_str());
        if (server_addr.sin_addr.s_addr == INADDR_NONE) {
#else
        if (inet_pton(AF_INET, ip_.c_str(), &server_addr.sin_addr) <= 0) {
#endif
            ROS_ERROR("RobotArmSDK: Invalid IP address: %s", ip_.c_str());
#ifdef _WIN32
            closesocket(socket_);
#else
            close(socket_);
#endif
            socket_ = INVALID_SOCKET;
            return false;
        }
        
        // 连接
        if (::connect(socket_, (struct sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
            ROS_ERROR("RobotArmSDK: Failed to connect to %s:%d", ip_.c_str(), port_);
#ifdef _WIN32
            closesocket(socket_);
#else
            close(socket_);
#endif
            socket_ = INVALID_SOCKET;
            return false;
        }
        
        connect_flag_.store(true);
        startReceiveThread();
        
        ROS_INFO("RobotArmSDK: Successfully connected to %s:%d", ip_.c_str(), port_);
        return true;
        
    } catch (const std::exception& e) {
        ROS_ERROR("RobotArmSDK: Connection failed: %s", e.what());
        return false;
    }
}

bool RobotArmSDK::disconnect() {
    connect_flag_.store(false);
    
    try {
        if (socket_ != INVALID_SOCKET) {
#ifdef _WIN32
            closesocket(socket_);
#else
            close(socket_);
#endif
            socket_ = INVALID_SOCKET;
        }
    } catch (const std::exception& e) {
        ROS_ERROR("RobotArmSDK: Error closing socket: %s", e.what());
        return false;
    }
    
    // 等待接收线程结束
    if (receive_thread_ && receive_thread_->joinable()) {
        receive_thread_->join();
    }
    
    ROS_INFO("RobotArmSDK: Connection closed");
    return true;
}

bool RobotArmSDK::reconnect(int retries, int delay_ms) {
    for (int attempt = 0; attempt < retries; ++attempt) {
        try {
            disconnect();
            std::this_thread::sleep_for(std::chrono::milliseconds(3000));
            
            if (connect()) {
                ROS_INFO("RobotArmSDK: Reconnection successful on attempt %d", attempt + 1);
                return true;
            }
        } catch (const std::exception& e) {
            ROS_WARN("RobotArmSDK: Reconnection attempt %d failed: %s", attempt + 1, e.what());
        }
        
        if (attempt < retries - 1) {
            std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms));
        }
    }
    
    ROS_ERROR("RobotArmSDK: All reconnection attempts failed");
    return false;
}

void RobotArmSDK::startReceiveThread() {
    if (receive_thread_ && receive_thread_->joinable()) {
        ROS_INFO("RobotArmSDK: Receive thread already running");
        return;
    }
    
    receive_thread_ = std::make_unique<std::thread>(&RobotArmSDK::receiveLoop, this);
}

void RobotArmSDK::receiveLoop() {
    const int buffer_size = 2048;
    std::vector<uint8_t> buffer(buffer_size);
    
    while (connect_flag_.load() && socket_ != INVALID_SOCKET) {
        try {
            // 使用select检查socket是否可读
            fd_set read_fds;
            FD_ZERO(&read_fds);
            FD_SET(socket_, &read_fds);
            
            struct timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;
            
            int result = select(socket_ + 1, &read_fds, nullptr, nullptr, &timeout);
            
            if (result > 0 && FD_ISSET(socket_, &read_fds)) {
                int bytes_received = recv(socket_, reinterpret_cast<char*>(buffer.data()), buffer_size, 0);
                
                if (bytes_received > 0) {
                    // 解析接收到的数据
                    std::string data(buffer.begin(), buffer.begin() + bytes_received);
                    
                    // 提取命令字 (假设在数据的第4-5字节)
                    if (bytes_received >= 6) {
                        std::stringstream ss;
                        ss << std::hex << std::setfill('0') << std::setw(2) << static_cast<int>(buffer[4]);
                        ss << std::hex << std::setfill('0') << std::setw(2) << static_cast<int>(buffer[5]);
                        std::string cmd_word = ss.str();
                        
                        // 查找JSON数据开始位置
                        size_t json_start = data.find('{');
                        if (json_start != std::string::npos) {
                            std::string json_data = data.substr(json_start);
                            parseStatus(json_data, cmd_word);
                        }
                    }
                } else if (bytes_received == 0) {
                    ROS_WARN("RobotArmSDK: Connection closed by remote host");
                    break;
                } else {
                    ROS_ERROR("RobotArmSDK: Receive error");
                    break;
                }
            } else if (result == SOCKET_ERROR) {
                ROS_ERROR("RobotArmSDK: Select error");
                break;
            }
            
        } catch (const std::exception& e) {
            ROS_ERROR("RobotArmSDK: Receive loop exception: %s", e.what());
            break;
        }
    }
    
    ROS_DEBUG("RobotArmSDK: Receive thread exiting");
}

void RobotArmSDK::parseStatus(const std::string& data, const std::string& cmd_word) {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    try {
        Json::Value root;
        Json::Reader reader;
        
        if (reader.parse(data, root)) {
            for (const auto& key : root.getMemberNames()) {
                std::string status_key = cmd_word + key;
                
                if (root[key].isString()) {
                    return_status_[status_key] = root[key].asString();
                } else {
                    Json::StreamWriterBuilder builder;
                    return_status_[status_key] = Json::writeString(builder, root[key]);
                }
            }
            
            ROS_DEBUG("RobotArmSDK: Status updated: %s", data.c_str());
            status_cv_.notify_all();
        } else {
            ROS_WARN("RobotArmSDK: Failed to parse JSON: %s", data.c_str());
        }
    } catch (const std::exception& e) {
        ROS_ERROR("RobotArmSDK: JSON parsing error: %s", e.what());
    }
}

// 在 RobotArmSDK 类中添加一个新函数
bool RobotArmSDK::waitForBoolResponse(const std::string& key, int timeout_ms) {
    std::unique_lock<std::mutex> lock(status_mutex_);

    auto timeout = std::chrono::steady_clock::now() + std::chrono::milliseconds(timeout_ms);

    while (return_status_.find(key) == return_status_.end()) {
        if (status_cv_.wait_until(lock, timeout) == std::cv_status::timeout) {
            ROS_WARN("RobotArmSDK: Timeout waiting for boolean response key: %s", key.c_str());
            return false; // 超时通常表示失败，返回false
        }
    }

    // 获取并尝试转换为bool
    Json::Value val = return_status_[key]; // 假设Json::Value可以从map中直接获取
    return_status_.erase(key); // 清除已使用的状态

    // 假设控制器返回的bool值在Json中是Json::booleanValue类型
    // 或者如果Python侧是"None"表示成功，那么在C++中可能对应一个空字符串
    // 这里需要根据实际协议确定Json::Value如何表示Python的None/True/False
    
    // 如果Python的"None"表示成功（空字符串），那么检查Json::Value是否为空或特定字符串
    if (val.isString() && val.asString().empty()) {
        return true; // 对应Python的None/空字符串表示成功
    } else if (val.isBool()) {
        return val.asBool(); // 如果直接返回了布尔值
    } else {
        ROS_WARN("RobotArmSDK: Unexpected type or value for boolean response key %s: %s", key.c_str(), val.toStyledString().c_str());
        return false; // 非预期的值，视为失败
    }
}

std::string RobotArmSDK::waitForResponse(const std::string& key, int timeout_ms) {
    std::unique_lock<std::mutex> lock(status_mutex_);

    auto timeout = std::chrono::steady_clock::now() + std::chrono::milliseconds(timeout_ms);

    while (return_status_.find(key) == return_status_.end()) {
        if (status_cv_.wait_until(lock, timeout) == std::cv_status::timeout) {
            ROS_WARN("RobotArmSDK: Timeout waiting for response key: %s", key.c_str());
            return "";
        }
    }

    std::string result = return_status_[key];
    return_status_.erase(key);  // 清除已使用的状态
    return result;
}

bool RobotArmSDK::sendCommand(uint16_t cmd_word, const Json::Value& cmd_data) {
    if (!connect_flag_.load()) {
        ROS_ERROR("RobotArmSDK: Not connected");
        return false;
    }

    try {
        // 构建JSON字符串
        std::string json_str;
        if (!cmd_data.isNull()) {
            Json::StreamWriterBuilder builder;
            json_str = Json::writeString(builder, cmd_data);
        } else {
            json_str = "{}";
        }

        // 创建数据帧
        auto frame = createFrame(cmd_word, json_str);

        ROS_DEBUG("RobotArmSDK: Sending command: 0x%04X, data: %s", cmd_word, json_str.c_str());

        // 发送数据
        int bytes_sent = send(socket_, reinterpret_cast<const char*>(frame.data()), frame.size(), 0);
        if (bytes_sent == SOCKET_ERROR) {
            ROS_ERROR("RobotArmSDK: Failed to send command");

            // 尝试重连
            if (reconnect()) {
                bytes_sent = send(socket_, reinterpret_cast<const char*>(frame.data()), frame.size(), 0);
                if (bytes_sent != SOCKET_ERROR) {
                    ROS_INFO("RobotArmSDK: Command sent successfully after reconnection");
                    return true;
                }
            }
            return false;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        return true;

    } catch (const std::exception& e) {
        ROS_ERROR("RobotArmSDK: Send command error: %s", e.what());
        return false;
    }
}

std::vector<uint8_t> RobotArmSDK::createFrame(uint16_t cmd_word, const std::string& data) {
    std::vector<uint8_t> frame;

    // 帧头 (2字节)
    frame.push_back((constants::FRAME_HEADER >> 8) & 0xFF);
    frame.push_back(constants::FRAME_HEADER & 0xFF);

    // 数据长度 (2字节，大端序)
    uint16_t data_length = static_cast<uint16_t>(data.length());
    frame.push_back((data_length >> 8) & 0xFF);
    frame.push_back(data_length & 0xFF);

    // 命令字 (2字节)
    frame.push_back((cmd_word >> 8) & 0xFF);
    frame.push_back(cmd_word & 0xFF);

    // 数据段
    for (char c : data) {
        frame.push_back(static_cast<uint8_t>(c));
    }

    // CRC32校验 (4字节)
    std::vector<uint8_t> crc_data(frame.begin() + 2, frame.end()); // 从长度字段开始
    uint32_t crc = calculateCRC32(crc_data);
    frame.push_back((crc >> 24) & 0xFF);
    frame.push_back((crc >> 16) & 0xFF);
    frame.push_back((crc >> 8) & 0xFF);
    frame.push_back(crc & 0xFF);

    return frame;
}

uint32_t RobotArmSDK::calculateCRC32(const std::vector<uint8_t>& data) {
    // 简化的CRC32实现，实际应用中可能需要更完整的实现
    uint32_t crc = 0xFFFFFFFF;

    for (uint8_t byte : data) {
        crc ^= byte;
        for (int i = 0; i < 8; ++i) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0xEDB88320;
            } else {
                crc >>= 1;
            }
        }
    }

    return ~crc;
}

// ========== 基础控制命令实现 ==========

bool RobotArmSDK::faultReset(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::FAULT_RESET, cmd_data)) {
        return false;
    }

    std::string response = waitForResponse("3202clearErrflag");
    bool success = (response == "true" || response == "1");

    if (success) {
        ROS_INFO("RobotArmSDK: Fault reset successful");
    } else {
        ROS_ERROR("RobotArmSDK: Fault reset failed");
    }

    return success;
}

void RobotArmSDK::rebootController() {
    sendCommand(constants::port_6001::REBOOT_CONTROLLER);
    ROS_INFO("RobotArmSDK: Controller reboot command sent");
}

bool RobotArmSDK::controllerInitFinishInquire() {
    if (!sendCommand(constants::port_6001::CONTROLLER_INIT_FINISH)) {
        return false;
    }

    std::string response = waitForResponse("4306initfinish");
    bool finished = (response == "true" || response == "1");

    ROS_INFO("RobotArmSDK: Controller initialization status: %s", finished ? "completed" : "not completed");
    return finished;
}

bool RobotArmSDK::controllerIpSet(int robot, const std::string& ip) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["ip"] = ip;

    if (!sendCommand(constants::port_6001::controller_ip::SET, cmd_data)) {
        return false;
    }

    std::string response = waitForResponse("4302ip");
    bool success = !response.empty();

    ROS_INFO("RobotArmSDK: Controller IP set to: %s, success: %s", ip.c_str(), success ? "true" : "false");
    return success;
}

std::string RobotArmSDK::controllerIpInquire(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::controller_ip::INQUIRE, cmd_data)) {
        return "";
    }

    std::string response = waitForResponse("4302ip");
    ROS_INFO("RobotArmSDK: Controller IP: %s", response.c_str());
    return response;
}

// 新增：完整的控制器IP设置接口
bool RobotArmSDK::controllerIpSet(const std::string& name, const std::string& address, 
                                 const std::string& gateway, const std::string& dns) {
    Json::Value cmd_data;
    cmd_data["name"] = name;
    cmd_data["address"] = address;
    cmd_data["gateway"] = gateway;
    cmd_data["dns"] = dns;

    if (!sendCommand(constants::port_6001::controller_ip::SET, cmd_data)) {
        return false;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    ROS_INFO("Modified controller network interface %s IP to %s, DNS to %s, Gateway to %s", 
             name.c_str(), address.c_str(), dns.c_str(), gateway.c_str());
    ROS_INFO("Controller will automatically restart after IP modification (no restart if IP is unchanged)");
    return true;
}

// 新增：完整的控制器IP查询接口
std::vector<std::map<std::string, std::string>> RobotArmSDK::controllerIpInquire() {
    if (!sendCommand(constants::port_6001::controller_ip::INQUIRE)) {
        return std::vector<std::map<std::string, std::string>>();
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    std::vector<std::map<std::string, std::string>> network_info;

    try {
        std::string num_response = waitForResponse("4303num");
        int num = std::stoi(num_response);
        
        ROS_INFO("Query controller IP");
        ROS_INFO("> Network IP count: %d", num);

        std::string network_response = waitForResponse("4303network");
        if (!network_response.empty()) {
            Json::Value network_data;
            Json::Reader reader;
            if (reader.parse(network_response, network_data) && network_data.isArray()) {
                for (int i = 0; i < num && i < static_cast<int>(network_data.size()); ++i) {
                    const Json::Value& network_item = network_data[i];
                    std::map<std::string, std::string> interface_info;
                    
                    if (network_item.isMember("address")) {
                        interface_info["address"] = network_item["address"].asString();
                    }
                    if (network_item.isMember("name")) {
                        interface_info["name"] = network_item["name"].asString();
                    }
                    if (network_item.isMember("dns")) {
                        interface_info["dns"] = network_item["dns"].asString();
                    }
                    if (network_item.isMember("gateway")) {
                        interface_info["gateway"] = network_item["gateway"].asString();
                    }
                    
                    network_info.push_back(interface_info);
                    
                    ROS_INFO("> Interface %s: %s, DNS: %s, Gateway: %s", 
                            interface_info["name"].c_str(), interface_info["address"].c_str(),
                            interface_info["dns"].c_str(), interface_info["gateway"].c_str());
                }
            }
        }
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse controller IP response: %s", e.what());
    }

    return network_info;
}

// ========== 运动控制命令实现 ==========

void RobotArmSDK::movj(int vel, CoordinateType coord, const JointAngles& pos, int robot) {
    if (pos.size() != constants::defaults::JOINT_COUNT) {
        ROS_ERROR("RobotArmSDK: Invalid joint angles count: %zu, expected: %zu", pos.size(), constants::defaults::JOINT_COUNT);
        return;
    }

    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["vel"] = vel;
    cmd_data["coord"] = static_cast<int>(coord);

    Json::Value pos_array(Json::arrayValue);
    for (double angle : pos) {
        pos_array.append(angle);
    }
    cmd_data["pos"] = pos_array;

    sendCommand(constants::port_6001::robot_movement::JOINT, cmd_data);

    ROS_INFO("RobotArmSDK: MOVJ command sent - Robot: %d, Vel: %d, Coord: %d", robot, vel, static_cast<int>(coord));
}

void RobotArmSDK::movl(int vel, CoordinateType coord, const JointAngles& pos, int robot) {
    if (pos.size() != constants::defaults::JOINT_COUNT) {
        ROS_ERROR("RobotArmSDK: Invalid joint angles count: %zu, expected: %zu", pos.size(), constants::defaults::JOINT_COUNT);
        return;
    }

    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["vel"] = vel;
    cmd_data["coord"] = static_cast<int>(coord);

    Json::Value pos_array(Json::arrayValue);
    for (double angle : pos) {
        pos_array.append(angle);
    }
    cmd_data["pos"] = pos_array;

    sendCommand(constants::port_6001::robot_movement::LINEAR, cmd_data);

    ROS_INFO("RobotArmSDK: MOVL command sent - Robot: %d, Vel: %d, Coord: %d", robot, vel, static_cast<int>(coord));
}

void RobotArmSDK::movc(int vel, CoordinateType coord, bool is_full,
                      const JointAngles& pos_one, const JointAngles& pos_two,
                      const JointAngles& pos_three, int robot) {
    if (pos_one.size() != constants::defaults::JOINT_COUNT ||
        pos_two.size() != constants::defaults::JOINT_COUNT ||
        pos_three.size() != constants::defaults::JOINT_COUNT) {
        ROS_ERROR("RobotArmSDK: Invalid joint angles count for MOVC");
        return;
    }

    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["vel"] = vel;
    cmd_data["coord"] = static_cast<int>(coord);
    cmd_data["isFull"] = is_full ? "true" : "false";

    Json::Value pos_one_array(Json::arrayValue);
    for (double angle : pos_one) {
        pos_one_array.append(angle);
    }
    cmd_data["posOne"] = pos_one_array;

    Json::Value pos_two_array(Json::arrayValue);
    for (double angle : pos_two) {
        pos_two_array.append(angle);
    }
    cmd_data["posTwo"] = pos_two_array;

    Json::Value pos_three_array(Json::arrayValue);
    for (double angle : pos_three) {
        pos_three_array.append(angle);
    }
    cmd_data["posThree"] = pos_three_array;

    sendCommand(constants::port_6001::robot_movement::CIRCULAR, cmd_data);

    ROS_INFO("RobotArmSDK: MOVC command sent - Robot: %d, Vel: %d, Coord: %d, IsFull: %s",
                robot, vel, static_cast<int>(coord), is_full ? "true" : "false");
}

void RobotArmSDK::movs(int vel, CoordinateType coord, int size, const Trajectory& trajectory, int robot) {
    if (trajectory.empty()) {
        ROS_ERROR("RobotArmSDK: Empty trajectory for MOVS");
        return;
    }

    for (const auto& pos : trajectory) {
        if (pos.size() != constants::defaults::JOINT_COUNT) {
            ROS_ERROR("RobotArmSDK: Invalid joint angles count in trajectory");
            return;
        }
    }

    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["vel"] = vel;
    cmd_data["coord"] = static_cast<int>(coord);
    cmd_data["size"] = size;

    Json::Value pos_array(Json::arrayValue);
    for (const auto& pos : trajectory) {
        Json::Value point_array(Json::arrayValue);
        for (double angle : pos) {
            point_array.append(angle);
        }
        pos_array.append(point_array);
    }
    cmd_data["pos"] = pos_array;

    sendCommand(constants::port_6001::robot_movement::SPLINE, cmd_data);

    ROS_INFO("RobotArmSDK: MOVS command sent - Robot: %d, Vel: %d, Coord: %d, Points: %zu",
                robot, vel, static_cast<int>(coord), trajectory.size());
}

// ========== 状态查询命令实现 ==========

JointAngles RobotArmSDK::currentPosInquiry(CoordinateType coord, int robot) {
    if (static_cast<int>(coord) < -1 || static_cast<int>(coord) > 3) {
        ROS_WARN("Invalid coordinate mode: %d, should be -1, 0(Joint), 1(Cart), 2(Tool), or 3(User)", static_cast<int>(coord));
        return JointAngles();
    }

    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["coord"] = static_cast<int>(coord);

    if (!sendCommand(constants::port_6001::CURRENTPOS_INQUIRE, cmd_data)) {
        return JointAngles();
    }

    std::string response = waitForResponse("2A03pos");

    JointAngles result;
    int index = 0;
    try {
        Json::Value root;
        Json::Reader reader;
        reader.parse(response, root);
        if (root.isArray()) {
            for (const auto& value : root) {
                if (index < result.size()) { 
                    result[index] = value.asDouble();
                    index++;
                } else {
                    ROS_WARN("JSON array has too many elements for JointAngles std::array.");
                    break; 
                }
            }
        }
    } catch (const std::exception& e) {
        ROS_ERROR("RobotArmSDK: Failed to parse current position: %s", e.what());
    }

    // 处理坐标转换
    if (static_cast<int>(coord) >= 1 && static_cast<int>(coord) <= 3) {
        // 直角、工具、用户坐标：前3位从毫米转换为米
        for (int i = 0; i < 3; ++i) {
            result[i] /= 1000.0;
        }
        // 移除第7位（如果存在）
        if (result.size() > 6) {
            // 这里需要特殊处理，因为JointAngles是固定大小的数组
            // 在实际使用中，可能需要调整数组大小或使用不同的数据结构
        }
    }

    // 四舍五入到4位小数
    for (auto& angle : result) {
        angle = std::round(angle * 10000.0) / 10000.0;
    }

    ROS_DEBUG("Current position: [%.4f, %.4f, %.4f, %.4f, %.4f, %.4f, %.4f]", 
              result[0], result[1], result[2], result[3], result[4], result[5], result[6]);

    return result;
}

std::tuple<double, double> RobotArmSDK::currentvelInquire(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::CURRENTVEL_INQUIRE, cmd_data)) {
        return std::make_tuple(0.0, 0.0);
    }

    std::string vel_response = waitForResponse("2A05vel");
    std::string max_vel_response = waitForResponse("2A05maxVel");

    double vel = 0.0, max_vel = 0.0;
    try {
        vel = std::stod(vel_response);
        max_vel = std::stod(max_vel_response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse current velocity response: %s", e.what());
    }

    ROS_INFO("Current motor velocity: %f RPM", vel);
    ROS_INFO("Max motor velocity: %f RPM", max_vel);

    return std::make_tuple(vel, max_vel);
}

std::tuple<double, double> RobotArmSDK::currenttorqInquire(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::CURRENTTORQ_INQUIRE, cmd_data)) {
        return std::make_tuple(0.0, 0.0);
    }

    std::string torq_response = waitForResponse("2A07torq");
    std::string max_torq_response = waitForResponse("2A07maxTorq");

    double torq = 0.0, max_torq = 0.0;
    try {
        torq = std::stod(torq_response);
        max_torq = std::stod(max_torq_response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse current torque response: %s", e.what());
    }

    ROS_INFO("Current motor torque: %f", torq);
    ROS_INFO("Max motor torque: %f", max_torq);

    return std::make_tuple(torq, max_torq);
}

std::tuple<double, double, std::vector<double>, std::vector<double>> RobotArmSDK::axisActualVelInquire(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::AXISACTUALVEL_INQUIRE, cmd_data)) {
        return std::make_tuple(0.0, 0.0, std::vector<double>(), std::vector<double>());
    }

    std::string actual_line_vel_response = waitForResponse("2A23actualLineVel");
    std::string max_actual_line_vel_response = waitForResponse("2A23maxActualLineVel");
    std::string axis_actual_vel_response = waitForResponse("2A23axisActualVel");
    std::string max_axis_actual_vel_response = waitForResponse("2A23maxAxisActualVel");

    double actual_line_vel = 0.0, max_actual_line_vel = 0.0;
    std::vector<double> axis_actual_vel, max_axis_actual_vel;

    try {
        actual_line_vel = std::stod(actual_line_vel_response);
        max_actual_line_vel = std::stod(max_actual_line_vel_response);

        // 解析轴速度数组
        Json::Value axis_vel_array, max_axis_vel_array;
        Json::Reader reader;
        
        if (reader.parse(axis_actual_vel_response, axis_vel_array) && axis_vel_array.isArray()) {
            for (const auto& vel : axis_vel_array) {
                axis_actual_vel.push_back(vel.asDouble());
            }
        }
        
        if (reader.parse(max_axis_actual_vel_response, max_axis_vel_array) && max_axis_vel_array.isArray()) {
            for (const auto& vel : max_axis_vel_array) {
                max_axis_actual_vel.push_back(vel.asDouble());
            }
        }
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse axis actual velocity response: %s", e.what());
    }

    ROS_INFO("Current end line velocity: %f mm/s", actual_line_vel);
    ROS_INFO("Max end line velocity: %f mm/s", max_actual_line_vel);
    ROS_INFO("Axis actual velocities: %zu axes", axis_actual_vel.size());
    ROS_INFO("Max axis velocities: %zu axes", max_axis_actual_vel.size());

    return std::make_tuple(actual_line_vel, max_actual_line_vel, axis_actual_vel, max_axis_actual_vel);
}

double RobotArmSDK::speedInquire(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::speed_control::INQUIRE, cmd_data)) {
        return 0.0;
    }

    std::string response = waitForResponse("2603speed");

    try {
        return std::stod(response);
    } catch (const std::exception& e) {
        ROS_ERROR("RobotArmSDK: Failed to parse speed: %s", e.what());
        return 0.0;
    }
}

bool RobotArmSDK::speedSet(double speed, int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["speed"] = speed;

    if (!sendCommand(constants::port_6001::speed_control::SET, cmd_data)) {
        return false;
    }

    std::string response = waitForResponse("2603speed");
    bool success = !response.empty();

    ROS_INFO("RobotArmSDK: Speed set to: %.2f, success: %s", speed, success ? "true" : "false");
    return success;
}

void RobotArmSDK::coordModeSet(int coord, int robot) {
    if (coord < 0 || coord > 3) {
        ROS_WARN("Invalid coordinate mode: %d, should be 0(Joint), 1(Cart), 2(Tool), or 3(User)", coord);
        return;
    }

    Json::Value cmd_data;
    cmd_data["coord"] = coord;
    cmd_data["robot"] = robot;

    sendCommand(constants::port_6001::coord_mode::SET, cmd_data);

    std::string coord_desc;
    switch (coord) {
        case 0: coord_desc = "关节坐标(Joint)"; break;
        case 1: coord_desc = "直角坐标(Cart)"; break;
        case 2: coord_desc = "工具坐标(Tool)"; break;
        case 3: coord_desc = "用户坐标(User)"; break;
        default: coord_desc = "未知坐标"; break;
    }
    ROS_INFO("Set coordinate mode to: %s", coord_desc.c_str());
}

int RobotArmSDK::coordModeInquire(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::coord_mode::INQUIRE, cmd_data)) {
        return -1;
    }

    std::string response = waitForResponse("2203coord");

    int coord = 0;
    try {
        coord = std::stoi(response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse coordinate mode response: %s", e.what());
        return -1;
    }

    std::string coord_desc;
    switch (coord) {
        case 0: coord_desc = "关节坐标(Joint)"; break;
        case 1: coord_desc = "直角坐标(Cart)"; break;
        case 2: coord_desc = "工具坐标(Tool)"; break;
        case 3: coord_desc = "用户坐标(User)"; break;
        default: coord_desc = "未知坐标"; break;
    }
    ROS_INFO("Current coordinate mode: %s", coord_desc.c_str());
    return coord;
}

bool RobotArmSDK::setServoPointMotionControl(std::string enable, int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["switch"] = enable; // Python中使用bool类型，C++直接传递bool

    if (!sendCommand(constants::port_6001::servo_control::OPEN, cmd_data)) {
        ROS_ERROR("Failed to send command for setting servo point motion control.");
        return false;
    }

    // Python代码中成功时cause为None，对应C++中waitForResponse返回空字符串
    bool success = waitForBoolResponse("95A3cause"); 

    ROS_INFO("开关伺服点位运动控制状态设置: %s; 状态: %d", enable.c_str(), static_cast<int>(success));

    return success; 
}


bool RobotArmSDK::servoPointMotionControl(int end, int sum, int count, const std::vector<JointAngles>& posVec, int robot) {
    // 检查PosVec是否为空，以及每个JointAngles的维度是否正确
    if (posVec.empty()) {
        ROS_ERROR("RobotArmSDK: PosVec cannot be empty.");
        return false;
    }
    for (const auto& pos : posVec) {
        if (pos.size() != constants::defaults::JOINT_COUNT) {
            ROS_ERROR("RobotArmSDK: Invalid joint angles count in PosVec: %zu, expected: %zu", pos.size(), constants::defaults::JOINT_COUNT);
            return false;
        }
    }

    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["end"] = end;
    cmd_data["sum"] = sum;
    cmd_data["count"] = count;

    Json::Value pos_vec_json(Json::arrayValue);
    for (const auto& single_pos : posVec) {
        Json::Value single_pos_array(Json::arrayValue);
        for (double angle : single_pos) {
            single_pos_array.append(angle);
        }
        pos_vec_json.append(single_pos_array);
    }
    cmd_data["PosVec"] = pos_vec_json;

    if (!sendCommand(constants::port_6001::servo_control::MOVE, cmd_data)) {
        ROS_ERROR("Failed to send command for servo point motion control.");
        return false;
    }

    std::string response = waitForResponse("95A6cause");
    
    if (response == "notStart") {
        ROS_INFO("未开启伺服点位运动控制模式");
        return false;
    } else if (response == "dataErr") {
        ROS_INFO("数据错误");
        return false;
    } else if (response == "termination") {
        ROS_INFO("发送端终止了正在传输的数据");
        return false;
    } else if (response == "cacheFull") {
        ROS_INFO("缓存区已满(最大缓存 6 条轨迹)");
        return false;
    }
    // 成功情况，response为空字符串，Python代码中返回(None, True)
    return response.empty();
}


// ========== 队列运动控制实现 ==========

bool RobotArmSDK::directMotionModeSet(bool enable, int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["enable"] = enable;

    if (!sendCommand(constants::port_6001::direct_motion::MODE_SET, cmd_data)) {
        return false;
    }

    std::string response = waitForResponse("50b2directmotion");
    bool success = !response.empty();

    ROS_INFO("Direct motion mode %s, success: %s",
                enable ? "enabled" : "disabled", success ? "true" : "false");
    return success;
}

bool RobotArmSDK::directMotionInsertInstrvec(const JointAngles& pos,
                                            int acc, int dec, int pl,
                                            int velocity, const std::string& imove_coord,
                                            int move_type) {
    if (pos.size() != constants::defaults::JOINT_COUNT) {
        ROS_ERROR("Invalid joint angles count: %zu, expected: %zu", pos.size(), constants::defaults::JOINT_COUNT);
        return false;
    }

    // 构建复杂的JSON数据结构，对应Python版本的格式
    Json::Value cmd_data;
    Json::Value data_array(Json::arrayValue);
    Json::Value data_item;

    // 参数设置
    data_item["ParaACC"] = Json::Value();
    data_item["ParaACC"]["data"] = acc;
    data_item["ParaACC"]["secondvalue"] = 0;
    data_item["ParaACC"]["value"] = 0;
    data_item["ParaACC"]["varname"] = "";

    data_item["ParaDEC"] = Json::Value();
    data_item["ParaDEC"]["data"] = dec;
    data_item["ParaDEC"]["secondvalue"] = 0;
    data_item["ParaDEC"]["value"] = 0;
    data_item["ParaDEC"]["varname"] = "";

    data_item["ParaPL"] = Json::Value();
    data_item["ParaPL"]["data"] = pl;
    data_item["ParaPL"]["secondvalue"] = 0;
    data_item["ParaPL"]["value"] = 0;
    data_item["ParaPL"]["varname"] = "";

    data_item["ParaSPIN"] = Json::Value();
    data_item["ParaSPIN"]["data"] = 0.0;
    data_item["ParaSPIN"]["secondvalue"] = 0;
    data_item["ParaSPIN"]["value"] = 0;
    data_item["ParaSPIN"]["varname"] = "";

    data_item["ParaSYNC"] = Json::Value();
    data_item["ParaSYNC"]["data"] = 0.0;
    data_item["ParaSYNC"]["secondvalue"] = 0;
    data_item["ParaSYNC"]["value"] = 0;
    data_item["ParaSYNC"]["varname"] = "";

    data_item["ParaTIME"] = Json::Value();
    data_item["ParaTIME"]["data"] = 0.0;
    data_item["ParaTIME"]["secondvalue"] = 0;
    data_item["ParaTIME"]["value"] = 0;
    data_item["ParaTIME"]["varname"] = "";

    data_item["ParaV"] = Json::Value();
    data_item["ParaV"]["data"] = velocity;
    data_item["ParaV"]["m_vUnit"] = 2;
    data_item["ParaV"]["secondvalue"] = 0;
    data_item["ParaV"]["value"] = 0;
    data_item["ParaV"]["varname"] = "";

    // 机器人位置数据
    Json::Value robot_pos;
    robot_pos["ctype"] = 1;

    // 构建21位数据数组
    Json::Value pos_data(Json::arrayValue);
    for (int i = 0; i < 7; ++i) {
        pos_data.append(0.0);
    }
    for (size_t i = 0; i < pos.size(); ++i) {
        pos_data.append(pos[i]);
    }
    for (int i = 0; i < 7; ++i) {
        pos_data.append(0.0);
    }
    robot_pos["data"] = pos_data;

    robot_pos["key"] = "";

    // 构建paraVarData数组
    Json::Value para_var_data(Json::arrayValue);
    for (int i = 0; i < 7; ++i) {
        Json::Value var_item;
        var_item["data"] = 0.0;
        var_item["secondvalue"] = 0;
        var_item["value"] = 0;
        var_item["varname"] = "";
        para_var_data.append(var_item);
    }
    for (size_t i = 0; i < pos.size(); ++i) {
        Json::Value var_item;
        var_item["data"] = pos[i];
        var_item["secondvalue"] = 0;
        var_item["value"] = 0;
        var_item["varname"] = "";
        para_var_data.append(var_item);
    }
    for (int i = 0; i < 6; ++i) {
        Json::Value var_item;
        var_item["data"] = 0.0;
        var_item["secondvalue"] = 0;
        var_item["value"] = 0;
        var_item["varname"] = "";
        para_var_data.append(var_item);
    }
    robot_pos["paraVarData"] = para_var_data;

    data_item["RobotPos"] = robot_pos;

    // 其他参数
    data_item["ctype"] = 0;
    data_item["imovecoord"] = imove_coord;
    data_item["length"] = 0.0;
    data_item["logout"] = false;
    data_item["margin"] = 0.0;
    data_item["offsetAxis"] = 0;
    data_item["para"] = 0;
    data_item["polish"] = 0;
    data_item["polishAngle"] = 0.0;
    data_item["polishID"] = 1;
    data_item["posidname"] = "";
    data_item["posidtype"] = 0;
    data_item["positionId"] = "";
    data_item["radius"] = 0.0;
    data_item["side"] = 0.0;
    data_item["type"] = move_type;
    data_item["userParamInt"] = 0;
    data_item["userParamString"] = "";
    data_item["width"] = 0.0;

    data_array.append(data_item);
    cmd_data["data"] = data_array;
    cmd_data["robot"] = 1;

    if (!sendCommand(constants::port_6001::direct_motion::INSERT_INSTRVEC, cmd_data)) {
        return false;
    }

    ROS_DEBUG("Direct motion instruction vector inserted");
    return true;
}

bool RobotArmSDK::directMotionModeSuspend(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::direct_motion::MODE_SUSPEND, cmd_data)) {
        return false;
    }

    ROS_INFO("Direct motion mode suspended");
    std::this_thread::sleep_for(std::chrono::seconds(2));
    return true;
}

bool RobotArmSDK::directMotionModeStart(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::direct_motion::MODE_START, cmd_data)) {
        return false;
    }

    ROS_INFO("Direct motion mode started");
    std::this_thread::sleep_for(std::chrono::seconds(2));
    return true;
}

bool RobotArmSDK::directMotionModeStop(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::direct_motion::MODE_STOP, cmd_data)) {
        return false;
    }

    ROS_INFO("Direct motion mode stopped");
    std::this_thread::sleep_for(std::chrono::seconds(2));
    return true;
}

bool RobotArmSDK::directMotionKeepPowerOn(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::direct_motion::KEEP_POWER_ON, cmd_data)) {
        return false;
    }

    ROS_INFO("Direct motion keep power on set");
    std::this_thread::sleep_for(std::chrono::seconds(2));
    return true;
}

// ========== 点动操作命令实现 ==========

bool RobotArmSDK::jogOperationMove(int axis, int direction) {
    Json::Value cmd_data;
    cmd_data["axis"] = axis;
    cmd_data["direction"] = direction;

    if (!sendCommand(constants::port_6001::jog_operation::MOVE, cmd_data)) {
        return false;
    }

    ROS_DEBUG("Jog operation move - Axis: %d, Direction: %d", axis, direction);
    return true;
}

bool RobotArmSDK::jogOperationStop(int axis) {
    Json::Value cmd_data;
    cmd_data["axis"] = axis;

    if (!sendCommand(constants::port_6001::jog_operation::STOP, cmd_data)) {
        return false;
    }

    ROS_INFO("Jog operation stopped - Axis: %d", axis);
    return true;
}

bool RobotArmSDK::jogJointParameterSet(int axis_num, int max_speed, int max_acc) {
    Json::Value cmd_data;
    cmd_data["AxisNum"] = axis_num;
    cmd_data["MaxSpeed"] = max_speed;
    cmd_data["MaxAcc"] = max_acc;

    if (!sendCommand(constants::port_6001::jog_joint_parameter::SET, cmd_data)) {
        return false;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    ROS_INFO("Jog joint parameter set - Axis: %d, MaxSpeed: %d, MaxAcc: %d",
                axis_num, max_speed, max_acc);
    return true;
}

std::tuple<int, int, int> RobotArmSDK::jogJointParameterInquire(int axis_num) {
    Json::Value cmd_data;
    cmd_data["AxisNum"] = axis_num;

    if (!sendCommand(constants::port_6001::jog_joint_parameter::INQUIRE, cmd_data)) {
        return std::make_tuple(0, 0, 0);
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    std::string axis_response = waitForResponse("2606AxisNum");
    std::string speed_response = waitForResponse("2606MaxSpeed");
    std::string acc_response = waitForResponse("2606MaxAcc");

    int axis = 0, max_speed = 0, max_acc = 0;
    try {
        axis = std::stoi(axis_response);
        max_speed = std::stoi(speed_response);
        max_acc = std::stoi(acc_response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse jog joint parameter response: %s", e.what());
    }

    ROS_INFO("Jog joint parameter - Axis: %d, MaxSpeed: %d, MaxAcc: %d",
                axis, max_speed, max_acc);

    return std::make_tuple(axis, max_speed, max_acc);
}

bool RobotArmSDK::jogSensitivitySet(double sensitivity) {
    Json::Value cmd_data;
    cmd_data["Sensitivity"] = sensitivity;

    if (!sendCommand(constants::port_6001::jog_sensitivity::SET, cmd_data)) {
        return false;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    ROS_INFO("Jog sensitivity set to: %f", sensitivity);
    return true;
}

double RobotArmSDK::jogSensitivityInquire() {
    if (!sendCommand(constants::port_6001::jog_sensitivity::INQUIRE)) {
        return 0.0;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    std::string response = waitForResponse("260CSensitivity");

    double sensitivity = 0.0;
    try {
        sensitivity = std::stod(response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse jog sensitivity response: %s", e.what());
    }

    ROS_INFO("Jog sensitivity: %f degrees", sensitivity);
    return sensitivity;
}

void RobotArmSDK::interpolationModeSet(int interpolationMethod, double absolutePosResolution,
                                       int runDelayTime, int stopTime) {
    if (interpolationMethod < 0 || interpolationMethod > 2) {
        ROS_WARN("Invalid interpolation method: %d, should be 0(S-type), 1(Trapezoid), or 2(AccAcc)", interpolationMethod);
        return;
    }

    if (absolutePosResolution < 0.0001 || absolutePosResolution > 0.1) {
        ROS_WARN("Absolute position resolution should be between 0.0001 and 0.1");
        return;
    }

    if (runDelayTime < 50 || runDelayTime > 20000) {
        ROS_WARN("Run delay time should be between 50 and 20000");
        return;
    }

    if (stopTime < 240 || stopTime > 2000) {
        ROS_WARN("Stop time should be between 240 and 2000");
        return;
    }

    Json::Value cmd_data;
    cmd_data["interpolationMethod"] = interpolationMethod;
    cmd_data["absolutePosResolution"] = absolutePosResolution;
    cmd_data["runDelayTime"] = runDelayTime;
    cmd_data["stopTime"] = stopTime;

    sendCommand(constants::port_6001::interpolation_mode::SET, cmd_data);
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    std::string method_desc;
    switch (interpolationMethod) {
        case 0: method_desc = "S型"; break;
        case 1: method_desc = "梯形"; break;
        case 2: method_desc = "加加插补"; break;
        default: method_desc = "未知"; break;
    }

    ROS_INFO("Set motion parameters:");
    ROS_INFO("  Interpolation method: %s", method_desc.c_str());
    ROS_INFO("  Absolute position resolution: %f degrees", absolutePosResolution);
    ROS_INFO("  Run delay time: %d ms", runDelayTime);
    ROS_INFO("  Stop time: %d ms", stopTime);
}

std::tuple<int, double, int, int> RobotArmSDK::interpolationModeInquire() {
    if (!sendCommand(constants::port_6001::interpolation_mode::INQUIRE)) {
        return std::make_tuple(0, 0.0, 0, 0);
    }

    std::string method_response = waitForResponse("2803interpolationMethod");
    std::string resolution_response = waitForResponse("2803absolutePosResolution");
    std::string delay_response = waitForResponse("2803runDelayTime");
    std::string stop_response = waitForResponse("2803stopTime");

    int interpolationMethod = 0;
    double absolutePosResolution = 0.0;
    int runDelayTime = 0, stopTime = 0;

    try {
        interpolationMethod = std::stoi(method_response);
        absolutePosResolution = std::stod(resolution_response);
        runDelayTime = std::stoi(delay_response);
        stopTime = std::stoi(stop_response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse interpolation mode response: %s", e.what());
    }

    std::string method_desc;
    switch (interpolationMethod) {
        case 0: method_desc = "S型"; break;
        case 1: method_desc = "梯形"; break;
        case 2: method_desc = "加加插补"; break;
        default: method_desc = "未知"; break;
    }

    ROS_INFO("Motion parameters:");
    ROS_INFO("  Interpolation method: %s", method_desc.c_str());
    ROS_INFO("  Absolute position resolution: %f degrees", absolutePosResolution);
    ROS_INFO("  Run delay time: %d ms", runDelayTime);
    ROS_INFO("  Stop time: %d ms", stopTime);

    return std::make_tuple(interpolationMethod, absolutePosResolution, runDelayTime, stopTime);
}

void RobotArmSDK::jogRectparameterSet(int MaxSpeed, int MaxAcc) {
    Json::Value cmd_data;
    cmd_data["MaxSpeed"] = MaxSpeed;
    cmd_data["MaxAcc"] = MaxAcc;

    if (!sendCommand(constants::port_6001::jog_rect_parameter::SET, cmd_data)) {
        ROS_ERROR("Failed to send jog rect parameter set command.");
        return;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    ROS_INFO("Jog rect parameter set - MaxSpeed: %d, MaxAcc: %d", MaxSpeed, MaxAcc);
}

std::tuple<int, int> RobotArmSDK::jogRectparameterInquire() {
    if (!sendCommand(constants::port_6001::jog_rect_parameter::INQUIRE)) {
        return std::make_tuple(0, 0);
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    int MaxSpeed = 0, MaxAcc = 0;
    try {
        std::string MaxSpeed_response = waitForResponse("2609MaxSpeed");
        std::string MaxAcc_response = waitForResponse("2609MaxAcc");

        MaxSpeed = std::stoi(MaxSpeed_response);
        MaxAcc = std::stoi(MaxAcc_response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse jog rect parameter response: %s", e.what());
    }

    ROS_INFO("Jog rect parameter - MaxSpeed: %d mm/s, MaxAcc: %d mm/s2", MaxSpeed, MaxAcc);
    return std::make_tuple(MaxSpeed, MaxAcc);
}

// ========== 7000端口多点运动控制实现 ==========

std::tuple<std::string, bool> RobotArmSDK::servoPointMotionControlAdvanced(
    int robot, int end, int sum, int count, const Trajectory& pos_vec) {

    if (port_ != 7000) {
        ROS_ERROR("Advanced servo control commands require port 7000");
        return std::make_tuple("portError", false);
    }

    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["end"] = end;
    cmd_data["sum"] = sum;
    cmd_data["count"] = count;

    Json::Value pos_vec_array(Json::arrayValue);
    for (const auto& pos : pos_vec) {
        Json::Value pos_array(Json::arrayValue);
        for (double angle : pos) {
            pos_array.append(angle);
        }
        pos_vec_array.append(pos_array);
    }
    cmd_data["PosVec"] = pos_vec_array;

    if (!sendCommand(constants::port_6001::servo_control::MOVE, cmd_data)) {
        return std::make_tuple("sendError", false);
    }

    std::string cause = waitForResponse("95A6cause");

    if (cause == "notStart") {
        ROS_INFO("Servo point motion control mode not started");
        return std::make_tuple(cause, false);
    } else if (cause == "dataErr") {
        ROS_INFO("Data error");
        return std::make_tuple(cause, false);
    } else if (cause == "termination") {
        ROS_INFO("Sender terminated the data transmission");
        return std::make_tuple(cause, false);
    } else if (cause == "cacheFull") {
        ROS_INFO("Cache full (max 6 trajectories)");
        return std::make_tuple(cause, false);
    } else if (cause.empty()) {
        return std::make_tuple(cause, true);
    }

    return std::make_tuple(cause, false);
}

bool RobotArmSDK::multiPointMove(const Trajectory& target_vecs) {
    if (port_ != 7000) {
        ROS_ERROR("Multi-point move commands require port 7000");
        return false;
    }

    Json::Value cmd_data;
    cmd_data["robot"] = 1;
    cmd_data["clearBuffer"] = 1;
    cmd_data["targetMode"] = 0;

    Json::Value cfg;
    cfg["coord"] = "ACS";
    cfg["extMove"] = 0;
    cfg["sync"] = 0;
    cfg["speed"] = 100;
    cfg["acc"] = 100;
    cfg["pl"] = 5;
    cfg["moveMode"] = "MOVC";
    cmd_data["cfg"] = cfg;

    Json::Value target_vec_array(Json::arrayValue);
    for (const auto& pos : target_vecs) {
        Json::Value target_item;
        Json::Value pos_array(Json::arrayValue);
        for (double angle : pos) {
            pos_array.append(angle);
        }
        target_item["pos"] = pos_array;
        target_vec_array.append(target_item);
    }
    cmd_data["targetVec"] = target_vec_array;

    if (!sendCommand(constants::port_7000::MULTI_POINT, cmd_data)) {
        return false;
    }

    ROS_INFO("Multi-point move command sent with %zu points", target_vecs.size());
    std::this_thread::sleep_for(std::chrono::seconds(1));
    return true;
}

// ========== 控制器配置命令实现 ==========

bool RobotArmSDK::controlCycleSet(int control_cycle) {
    Json::Value cmd_data;
    cmd_data["controlCycle"] = control_cycle;

    if (!sendCommand(constants::port_6001::control_cycle::SET, cmd_data)) {
        return false;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    ROS_INFO("Robot communication cycle set to: %d ms", control_cycle);
    ROS_INFO("Robot communication cycle setting takes effect after controller restart");
    return true;
}

int RobotArmSDK::controlCycleInquire() {
    if (!sendCommand(constants::port_6001::control_cycle::INQUIRE)) {
        return 0;
    }

    std::string response = waitForResponse("2E09controlCycle");

    int control_cycle = 0;
    try {
        control_cycle = std::stoi(response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse control cycle response: %s", e.what());
    }

    ROS_INFO("Robot communication cycle: %d ms", control_cycle);
    return control_cycle;
}

int RobotArmSDK::deadmanStatusSet(int deadman) {
    Json::Value cmd_data;
    cmd_data["deadman"] = deadman;

    if (!sendCommand(constants::port_6001::deadman_status::SET, cmd_data)) {
        return -1;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    std::string status_desc = (deadman == 0) ? "下电状态" :
                             (deadman == 1) ? "上电状态" : "未知状态";
    ROS_INFO("Set power status: %s", status_desc.c_str());
    return deadman;
}

int RobotArmSDK::deadmanStatusInquire() {
    if (!sendCommand(constants::port_6001::deadman_status::INQUIRE)) {
        return -1;
    }

    std::string response = waitForResponse("2303deadman");

    int deadman = 0;
    try {
        deadman = std::stoi(response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse deadman status response: %s", e.what());
        return -1;
    }

    std::string status_desc = (deadman == 0) ? "下电状态" :
                             (deadman == 1) ? "上电状态" : "未知状态";
    ROS_INFO("Query power status: %s", status_desc.c_str());
    return deadman;
}

int RobotArmSDK::servoStatusSet(int robot, int status) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["status"] = status;

    if (!sendCommand(constants::port_6001::servo_commands::SET, cmd_data)) {
        return -1;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    std::string status_desc;
    switch (status) {
        case 0: status_desc = "伺服 下电"; break;
        case 1: status_desc = "伺服 上电"; break;
        case 2: status_desc = "伺服 使能"; break;
        case 3: status_desc = "伺服 运行"; break;
        default: status_desc = "未知状态"; break;
    }
    ROS_INFO("Set servo status: %s", status_desc.c_str());
    return status;
}

int RobotArmSDK::servoStatusInquire(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::servo_commands::INQUIRE, cmd_data)) {
        return -1;
    }

    std::string response = waitForResponse("2003status");

    int status = 0;
    try {
        status = std::stoi(response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse servo status response: %s", e.what());
        return -1;
    }

    std::string status_desc;
    switch (status) {
        case 0: status_desc = "伺服 下电"; break;
        case 1: status_desc = "伺服 上电"; break;
        case 2: status_desc = "伺服 使能"; break;
        case 3: status_desc = "伺服 运行"; break;
        default: status_desc = "未知状态"; break;
    }
    ROS_INFO("Query servo status: %s", status_desc.c_str());
    return status;
}

int RobotArmSDK::servoConnectInquire() {
    if (!sendCommand(constants::port_6001::SERVO_CONNECT_INQUIRE)) {
        return -1;
    }

    std::string response = waitForResponse("5043servoType");

    int servo_type = 0;
    try {
        servo_type = std::stoi(response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse servo connect response: %s", e.what());
        return -1;
    }

    if (servo_type == 1) {
        ROS_INFO("Servo connection status: Virtual servo");
    } else if (servo_type == 2) {
        ROS_WARN("Servo connection status: No servo");
    } else {
        ROS_INFO("Servo connection status: Actual servo (type: %d)", servo_type);
    }

    return servo_type;
}

std::tuple<int, int, int, int, int> RobotArmSDK::servoInsideParmInquire(int robot, int servo_num) {
    if (servo_num < 1 || servo_num > 7) {
        ROS_WARN("Invalid servo number: %d, should be 1-7", servo_num);
        return std::make_tuple(0, 0, 0, 0, 0);
    }

    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["servoNum"] = servo_num;

    if (!sendCommand(constants::port_6001::servo_inside_parm::INQUIRE, cmd_data)) {
        return std::make_tuple(0, 0, 0, 0, 0);
    }

    std::this_thread::sleep_for(std::chrono::seconds(2));

    // 从return_status中提取值
    int error_code = 0, encoder_status_register1 = 0, encoder_status_register2 = 0;
    int encoder_single_turn_value = 0, holding_brake_status = 0;

    try {
        std::string servo_data_str = waitForResponse("5073servo");
        if (!servo_data_str.empty()) {
            Json::Value servo_data;
            Json::Reader reader;
            if (reader.parse(servo_data_str, servo_data)) {
                if (servo_data.isMember("编码器错误码") && servo_data["编码器错误码"].isMember("value")) {
                    error_code = servo_data["编码器错误码"]["value"].asInt();
                }
                if (servo_data.isMember("编码器状态寄存器1") && servo_data["编码器状态寄存器1"].isMember("value")) {
                    encoder_status_register1 = servo_data["编码器状态寄存器1"]["value"].asInt();
                }
                if (servo_data.isMember("编码器状态寄存器2") && servo_data["编码器状态寄存器2"].isMember("value")) {
                    encoder_status_register2 = servo_data["编码器状态寄存器2"]["value"].asInt();
                }
                if (servo_data.isMember("编码器单圈值") && servo_data["编码器单圈值"].isMember("value")) {
                    encoder_single_turn_value = servo_data["编码器单圈值"]["value"].asInt();
                }
                if (servo_data.isMember("抱闸手动控制") && servo_data["抱闸手动控制"].isMember("value")) {
                    holding_brake_status = servo_data["抱闸手动控制"]["value"].asInt();
                }
            }
        }
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse servo inside parameters: %s", e.what());
    }

    // 如果部分参数为空，等待2.8秒后重试一次查询
    if (error_code == 0 && encoder_status_register1 == 0 && encoder_status_register2 == 0 && 
        encoder_single_turn_value == 0 && holding_brake_status == 0) {
        ROS_WARN("Some servo parameters are empty, retrying query after 2.8 seconds");
        std::this_thread::sleep_for(std::chrono::milliseconds(2800));
        
        try {
            std::string servo_data_str = waitForResponse("5073servo");
            if (!servo_data_str.empty()) {
                Json::Value servo_data;
                Json::Reader reader;
                if (reader.parse(servo_data_str, servo_data)) {
                    if (servo_data.isMember("编码器错误码") && servo_data["编码器错误码"].isMember("value")) {
                        error_code = servo_data["编码器错误码"]["value"].asInt();
                    }
                    if (servo_data.isMember("编码器状态寄存器1") && servo_data["编码器状态寄存器1"].isMember("value")) {
                        encoder_status_register1 = servo_data["编码器状态寄存器1"]["value"].asInt();
                    }
                    if (servo_data.isMember("编码器状态寄存器2") && servo_data["编码器状态寄存器2"].isMember("value")) {
                        encoder_status_register2 = servo_data["编码器状态寄存器2"]["value"].asInt();
                    }
                    if (servo_data.isMember("编码器单圈值") && servo_data["编码器单圈值"].isMember("value")) {
                        encoder_single_turn_value = servo_data["编码器单圈值"]["value"].asInt();
                    }
                    if (servo_data.isMember("抱闸手动控制") && servo_data["抱闸手动控制"].isMember("value")) {
                        holding_brake_status = servo_data["抱闸手动控制"]["value"].asInt();
                    }
                }
            }
        } catch (const std::exception& e) {
            ROS_ERROR("Failed to parse servo inside parameters on retry: %s", e.what());
        }
    }

    ROS_INFO("Servo %d encoder error code: 0x%X", servo_num, error_code);
    ROS_INFO("Servo %d encoder single turn value: %d", servo_num, encoder_single_turn_value);
    ROS_INFO("Servo %d encoder status register1: %d", servo_num, encoder_status_register1);
    ROS_INFO("Servo %d encoder status register2: %d", servo_num, encoder_status_register2);
    ROS_INFO("Servo %d holding brake status: %d", servo_num, holding_brake_status);

    return std::make_tuple(error_code, encoder_status_register1, encoder_status_register2,
                          encoder_single_turn_value, holding_brake_status);
}

bool RobotArmSDK::servoInsideParmSet(int servo_num, const std::string& key_name,
                                    int key_value, int temporary_save, int robot) {
    if (servo_num < 1 || servo_num > 7) {
        ROS_WARN("Invalid servo number: %d, should be 1-7", servo_num);
        return false;
    }

    // 验证参数名
    std::vector<std::string> valid_params = {
        "6041", "6072", "60E0", "60E1",
        "位置环比例增益1", "初始化指令", "抱闸关闭延时", "抱闸启动延时", "抱闸手动控制",
        "母线电压值", "电压峰值", "电压最低值", "电机硬件版本", "电机编码", "电机软件版本", 
        "电流环比例增益", "电流环积分时间常数", "编码器单圈值", "编码器命令", "编码器多圈值",
        "编码器状态寄存器1", "编码器状态寄存器2", "编码器状态寄存器3", "编码器错误码", "警告状态",
        "输入侧温度值", "速度环增益", "速度环积分时间常数"
    };

    bool valid_param = false;
    for (const auto& param : valid_params) {
        if (key_name == param) {
            valid_param = true;
            break;
        }
    }

    if (!valid_param) {
        ROS_WARN("Invalid parameter name: %s, valid parameters: %zu", key_name.c_str(), valid_params.size());
        return false;
    }

    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["servoNum"] = servo_num;
    cmd_data["temporary_save"] = temporary_save;

    Json::Value servo_param;
    servo_param[key_name]["value"] = key_value;
    cmd_data["servo"] = servo_param;

    if (!sendCommand(constants::port_6001::servo_inside_parm::SET, cmd_data)) {
        return false;
    }

    ROS_INFO("Modified servo %d parameter \"%s\" value to %d", servo_num, key_name.c_str(), key_value);
    return true;
}

std::tuple<int, int, std::string, std::string> RobotArmSDK::slavetypeListRespond() {
    if (!sendCommand(constants::port_6001::SLAVETYPE_LIST_INQUIRE)) {
        return std::make_tuple(0, 0, "", "");
    }

    std::this_thread::sleep_for(std::chrono::seconds(2));

    int io_num = 0, servo_num = 0;
    std::string slave_type, slave_type_english;

    try {
        std::string io_response = waitForResponse("2e0fIONum");
        std::string servo_response = waitForResponse("2e0fservoNum");
        std::string type_response = waitForResponse("2e0fslaveType");
        std::string type_en_response = waitForResponse("2e0fslaveTypeEnglish");

        io_num = std::stoi(io_response);
        servo_num = std::stoi(servo_response);
        slave_type = type_response;
        slave_type_english = type_en_response;
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse slavetype list response: %s", e.what());
    }

    ROS_INFO("IO number: %d", io_num);
    ROS_INFO("Servo number: %d", servo_num);
    ROS_INFO("Servo type (Chinese): %s", slave_type.c_str());
    ROS_INFO("Servo type (English): %s", slave_type_english.c_str());

    return std::make_tuple(io_num, servo_num, slave_type, slave_type_english);
}

bool RobotArmSDK::operationModeSet(int mode) {
    Json::Value cmd_data;
    cmd_data["mode"] = mode;

    if (!sendCommand(constants::port_6001::operation_mode::SET, cmd_data)) {
        return false;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    std::string mode_desc;
    switch (mode) {
        case 0: mode_desc = "示教模式(Teach)"; break;
        case 1: mode_desc = "再现模式(Play)"; break;
        case 2: mode_desc = "运行模式(Repeat)"; break;
        default: mode_desc = "未知模式"; break;
    }
    ROS_INFO("Set operation mode: %s", mode_desc.c_str());
    return true;
}

int RobotArmSDK::operationModeInquire() {
    if (!sendCommand(constants::port_6001::operation_mode::INQUIRE)) {
        return -1;
    }

    std::string response = waitForResponse("2103mode");

    int mode = 0;
    try {
        mode = std::stoi(response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse operation mode response: %s", e.what());
        return -1;
    }

    std::string mode_desc;
    switch (mode) {
        case 0: mode_desc = "示教模式(Teach)"; break;
        case 1: mode_desc = "再现模式(Play)"; break;
        case 2: mode_desc = "运行模式(Repeat)"; break;
        default: mode_desc = "未知模式"; break;
    }
    ROS_INFO("Query operation mode: %s", mode_desc.c_str());
    return mode;
}

bool RobotArmSDK::jointParameterSet(int axis_num, double pos_sw_limit, double neg_sw_limit, int direction) {
    Json::Value cmd_data;
    cmd_data["AxisNum"] = axis_num;
    cmd_data["PosSWLimit"] = pos_sw_limit;
    cmd_data["NegSWLimit"] = neg_sw_limit;
    cmd_data["Direction"] = direction;

    if (!sendCommand(constants::port_6001::joint_parameter::SET, cmd_data)) {
        return false;
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    ROS_INFO("Set joint %d parameters - Pos limit: %.2f, Neg limit: %.2f, Direction: %d",
                axis_num, pos_sw_limit, neg_sw_limit, direction);
    return true;
}

std::tuple<double, double, double, double, double, double> RobotArmSDK::jointParameterInquire(int axis_num) {
    Json::Value cmd_data;
    cmd_data["AxisNum"] = axis_num;

    if (!sendCommand(constants::port_6001::joint_parameter::INQUIRE, cmd_data)) {
        return std::make_tuple(0.0, 0.0, 0.0, 0.0, 0.0, 0.0);
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    double pos_sw_limit = 0.0, neg_sw_limit = 0.0, rated_rot_speed = 0.0;
    double rated_de_rot_speed = 0.0, rated_vel = 0.0, de_rated_vel = 0.0;

    try {
        std::string pos_response = waitForResponse("3B03PosSWLimit");
        std::string neg_response = waitForResponse("3B03NegSWLimit");
        std::string rot_speed_response = waitForResponse("3B03RatedRotSpeed");
        std::string de_rot_speed_response = waitForResponse("3B03RatedDeRotSpeed");
        std::string vel_response = waitForResponse("3B03RatedVel");
        std::string de_vel_response = waitForResponse("3B03DeRatedVel");

        pos_sw_limit = std::stod(pos_response);
        neg_sw_limit = std::stod(neg_response);
        rated_rot_speed = std::stod(rot_speed_response);
        rated_de_rot_speed = std::stod(de_rot_speed_response);
        rated_vel = std::stod(vel_response);
        de_rated_vel = std::stod(de_vel_response);
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse joint parameter response: %s", e.what());
    }

    ROS_INFO("Joint %d parameters:", axis_num);
    ROS_INFO("  Positive limit: %.2f", pos_sw_limit);
    ROS_INFO("  Negative limit: %.2f", neg_sw_limit);
    ROS_INFO("  Rated rotation speed: %.2f", rated_rot_speed);
    ROS_INFO("  Rated deceleration rotation speed: %.2f", rated_de_rot_speed);
    ROS_INFO("  Rated velocity: %.2f", rated_vel);
    ROS_INFO("  Deceleration rated velocity: %.2f", de_rated_vel);

    return std::make_tuple(pos_sw_limit, neg_sw_limit, rated_rot_speed,
                          rated_de_rot_speed, rated_vel, de_rated_vel);
}

void RobotArmSDK::decareparameterSet(int MaxVel, int MaxAcc, int MaxDec,
                                    int MaxJerk, int MaxAttitudeVel, int SpeedLimitMode) {
    Json::Value cmd_data;
    Json::Value decare;
    decare["MaxVel"] = MaxVel;
    decare["MaxAcc"] = MaxAcc;
    decare["MaxDec"] = MaxDec;
    decare["MaxJerk"] = MaxJerk;
    decare["MaxAttitudeVel"] = MaxAttitudeVel;
    decare["SpeedLimitMode"] = SpeedLimitMode;
    cmd_data["Decare"] = decare;

    sendCommand(constants::port_6001::decare_parameter::SET, cmd_data);
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    std::string speed_limit_mode_desc = (SpeedLimitMode == 0) ? "位姿" : "位置";

    ROS_INFO("Set Cartesian coordinate parameters:");
    ROS_INFO("  Max velocity: %d mm/s", MaxVel);
    ROS_INFO("  Max acceleration: %d multiplier", MaxAcc);
    ROS_INFO("  Max deceleration: %d multiplier", MaxDec);
    ROS_INFO("  Max jerk: %d mm/s3", MaxJerk);
    ROS_INFO("  Max attitude velocity: %d °/s", MaxAttitudeVel);
    ROS_INFO("  Speed limit mode: %s", speed_limit_mode_desc.c_str());
}

std::tuple<int, int, int, int, int, int> RobotArmSDK::decareparameterInquire() {
    if (!sendCommand(constants::port_6001::decare_parameter::INQUIRE)) {
        return std::make_tuple(0, 0, 0, 0, 0, 0);
    }

    std::string decare_response = waitForResponse("3B06Decare");

    int MaxVel = 0, MaxAcc = 0, MaxDec = 0, MaxJerk = 0, MaxAttitudeVel = 0, SpeedLimitMode = 0;

    try {
        Json::Value decare_data;
        Json::Reader reader;
        if (reader.parse(decare_response, decare_data)) {
            MaxAcc = decare_data.get("MaxAcc", 0).asInt();
            MaxAttitudeVel = decare_data.get("MaxAttitudeVel", 0).asInt();
            MaxDec = decare_data.get("MaxDec", 0).asInt();
            MaxJerk = decare_data.get("MaxJerk", 0).asInt();
            MaxVel = decare_data.get("MaxVel", 0).asInt();
            SpeedLimitMode = decare_data.get("SpeedLimitMode", 0).asInt();
        }
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse decare parameter response: %s", e.what());
    }

    std::string speed_limit_mode_desc = (SpeedLimitMode == 0) ? "位姿" : "位置";

    ROS_INFO("Cartesian coordinate parameters:");
    ROS_INFO("  Max velocity: %d mm/s", MaxVel);
    ROS_INFO("  Max acceleration: %d multiplier", MaxAcc);
    ROS_INFO("  Max deceleration: %d multiplier", MaxDec);
    ROS_INFO("  Max jerk: %d mm/s3", MaxJerk);
    ROS_INFO("  Max attitude velocity: %d °/s", MaxAttitudeVel);
    ROS_INFO("  Speed limit mode: %s", speed_limit_mode_desc.c_str());

    return std::make_tuple(MaxVel, MaxAcc, MaxDec, MaxJerk, MaxAttitudeVel, SpeedLimitMode);
}

// ========== 作业控制命令实现 ==========

bool RobotArmSDK::jobSendDone(const std::string& job_name, int line, int continue_run, int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;
    cmd_data["jobName"] = job_name;
    cmd_data["line"] = line;
    cmd_data["continueRun"] = continue_run;

    if (!sendCommand(constants::port_6001::job_control::JOBSEND_DONE, cmd_data)) {
        return false;
    }

    ROS_INFO("Started job: %s, line: %d, continue: %s",
                job_name.c_str(), line, continue_run ? "true" : "false");
    return true;
}

bool RobotArmSDK::stopJobRun(int robot) {
    Json::Value cmd_data;
    cmd_data["robot"] = robot;

    if (!sendCommand(constants::port_6001::job_control::STOP_JOB_RUN, cmd_data)) {
        return false;
    }

    ROS_INFO("Stopped running job");
    return true;
}

std::vector<std::string> RobotArmSDK::jobFileListInquire() {
    if (!sendCommand(constants::port_6001::JOBFILE_LIST_INQUIRE)) {
        return std::vector<std::string>();
    }

    std::this_thread::sleep_for(std::chrono::seconds(1));

    std::vector<std::string> job_files;

    try {
        std::string response = waitForResponse("5533jobFileList");
        if (!response.empty()) {
            Json::Value job_list;
            Json::Reader reader;
            if (reader.parse(response, job_list) && job_list.isArray()) {
                for (const auto& job : job_list) {
                    if (job.isString()) {
                        job_files.push_back(job.asString());
                    }
                }
            }
        }
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to parse job file list response: %s", e.what());
    }

    ROS_INFO("Found %zu job files:", job_files.size());
    for (const auto& job : job_files) {
        ROS_INFO("  - %s", job.c_str());
    }

    return job_files;
}

} // namespace tcb710_sdk
