Panels:
  - Class: rviz/Displays
    Help Height: 0
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Global Options1
        - /Status1
        - /RobotModel1
        - /WrenchStampedSlave1
        - /MarkerArray1
      Splitter Ratio: 0.4892857074737549
    Tree Height: 809
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded:
      - /2D Pose Estimate1
      - /2D Nav Goal1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: ""
Preferences:
  PromptSaveOnExit: true
Toolbars:
  toolButtonStyle: 2
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 10
      Reference Frame: <Fixed Frame>
      Value: true
    - Class: rviz/TF
      Enabled: false
      Frame Timeout: 15
      Frames:
        All Enabled: true
      Marker Scale: 1
      Name: TF
      Show Arrows: true
      Show Axes: true
      Show Names: true
      Tree:
        {}
      Update Interval: 0
      Value: false
    - Alpha: 1
      Class: rviz/RobotModel
      Collision Enabled: false
      Enabled: true
      Links:
        All Links Enabled: true
        Expand Joint Details: false
        Expand Link Details: false
        Expand Tree: false
        Link Tree Style: Links in Alphabetic Order
        base:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_hand:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_hand_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_hand_tcp:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        follower_leftfinger:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link0:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link0_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link1_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link2:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link2_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link3:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link3_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link4:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link4_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link5:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link5_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link6:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link6_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link7:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link7_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        follower_link8:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        follower_rightfinger:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_hand:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_hand_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_hand_tcp:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        leader_leftfinger:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link0:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link0_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link1_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link2:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link2_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link3:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link3_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link4:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link4_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link5:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link5_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link6:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link6_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link7:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link7_sc:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        leader_link8:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        leader_rightfinger:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
      Name: RobotModel
      Robot Description: panda_teleop/robot_description
      TF Prefix: ""
      Update Interval: 0
      Value: true
      Visual Enabled: true
    - Alpha: 1
      Arrow Width: 0.20000000298023224
      Class: rviz/WrenchStamped
      Enabled: true
      Force Arrow Scale: 0.05000000074505806
      Force Color: 204; 51; 51
      Hide Small Values: true
      History Length: 1
      Name: WrenchStampedSlave
      Topic: /panda_teleop/follower_state_controller/F_ext
      Torque Arrow Scale: 0.20000000298023224
      Torque Color: 204; 204; 51
      Unreliable: false
      Value: true
    - Class: rviz/MarkerArray
      Enabled: true
      Marker Topic: /panda_teleop/teleop_joint_pd_example_controller/marker_labels
      Name: MarkerArray
      Namespaces:
        basic_shapes: true
      Queue Size: 100
      Value: true
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Default Light: true
    Fixed Frame: base
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/Select
    - Class: rviz/FocusCamera
    - Class: rviz/Measure
    - Class: rviz/SetInitialPose
      Theta std deviation: 0.2617993950843811
      Topic: /initialpose
      X std deviation: 0.5
      Y std deviation: 0.5
    - Class: rviz/SetGoal
      Topic: /move_base_simple/goal
    - Class: rviz/PublishPoint
      Single click: true
      Topic: /clicked_point
  Value: true
  Views:
    Current:
      Class: rviz/Orbit
      Distance: 4.382266998291016
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: -0.43465375900268555
        Y: 0.19732384383678436
        Z: 0.8964318633079529
      Focal Shape Fixed Size: false
      Focal Shape Size: 0.05000000074505806
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Pitch: 0.3903988003730774
      Target Frame: <Fixed Frame>
      Value: Orbit (rviz)
      Yaw: 6.2585835456848145
    Saved: ~
Window Geometry:
  Displays:
    collapsed: true
  Height: 1025
  Hide Left Dock: true
  Hide Right Dock: true
  QMainWindow State: 000000ff00000000fd0000000400000000000003fd00000366fc020000000dfb0000001200530065006c0065006300740069006f006e00000001e10000009b0000005c00fffffffb0000001e0054006f006f006c002000500072006f007000650072007400690065007302000001ed000001df00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c006100790073000000003d00000366000000c900fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261fb0000001c004d006f00740069006f006e0050006c0061006e006e0069006e006701000001bc000001e20000000000000000fb0000002e004d006f00740069006f006e0050006c0061006e006e0069006e00670020002d00200053006c00690064006500720000000000ffffffff0000000000000000fb000000120020002d00200053006c00690064006500720000000000ffffffff0000000000000000fb0000001c004d006f00740069006f006e0050006c0061006e006e0069006e006701000001ce000001d10000000000000000fb0000000a0049006d00610067006500000001a9000001f50000000000000000000000010000010f00000365fc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a00560069006500770073000000003f00000365000000a400fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e100000197000000030000073d0000003bfc0100000002fb0000000800540069006d006501000000000000073d000002eb00fffffffb0000000800540069006d006501000000000000045000000000000000000000073d0000036600000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: true
  Width: 1853
  X: 67
  Y: 27
