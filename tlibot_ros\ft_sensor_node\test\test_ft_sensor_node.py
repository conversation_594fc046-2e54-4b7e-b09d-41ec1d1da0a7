#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FT Sensor Node 测试脚本
用于验证节点功能和数据流
"""

import rospy
import time
import sys
from geometry_msgs.msg import WrenchStamped

class FTSensorTester:
    def __init__(self):
        rospy.init_node('ft_sensor_tester', anonymous=True)
        
        # 订阅话题
        self.subscriber = rospy.Subscriber(
            '/ft_sensor', 
            WrenchStamped, 
            self.callback
        )
        
        self.message_count = 0
        self.start_time = time.time()
        
        rospy.loginfo("FT Sensor Tester 已启动")
        rospy.loginfo("正在监听 /ft_sensor 话题...")
    
    def callback(self, msg):
        """处理接收到的传感器数据"""
        self.message_count += 1
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        # 计算消息频率
        if elapsed_time > 0:
            frequency = self.message_count / elapsed_time
        else:
            frequency = 0
        
        # 打印传感器数据
        rospy.loginfo("消息 #%d (频率: %.1f Hz)", self.message_count, frequency)
        rospy.loginfo("  时间戳: %s", msg.header.stamp)
        rospy.loginfo("  力: [%.3f, %.3f, %.3f]", 
                      msg.wrench.force.x, msg.wrench.force.y, msg.wrench.force.z)
        rospy.loginfo("  力矩: [%.3f, %.3f, %.3f]", 
                      msg.wrench.torque.x, msg.wrench.torque.y, msg.wrench.torque.z)
        rospy.loginfo("  Frame ID: %s", msg.header.frame_id)
        rospy.loginfo("---")
    
    def run(self):
        """运行测试"""
        try:
            rospy.spin()
        except KeyboardInterrupt:
            rospy.loginfo("测试被用户中断")
        except Exception as e:
            rospy.logerr("测试出错: %s", str(e))
        
        # 打印统计信息
        elapsed_time = time.time() - self.start_time
        if elapsed_time > 0:
            avg_frequency = self.message_count / elapsed_time
            rospy.loginfo("测试统计:")
            rospy.loginfo("  总消息数: %d", self.message_count)
            rospy.loginfo("  运行时间: %.2f 秒", elapsed_time)
            rospy.loginfo("  平均频率: %.2f Hz", avg_frequency)

def main():
    """主函数"""
    print("FT Sensor Node 测试工具")
    print("=" * 40)
    
    # 检查ROS是否运行
    try:
        rospy.get_master().getPid()
    except:
        print("错误: ROS master 未运行")
        print("请先运行: roscore")
        sys.exit(1)
    
    # 创建测试器
    tester = FTSensorTester()
    
    # 运行测试
    tester.run()

if __name__ == '__main__':
    main() 