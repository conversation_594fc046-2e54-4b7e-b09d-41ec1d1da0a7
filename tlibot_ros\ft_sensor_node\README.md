# FT Sensor Node

六维力传感器ROS节点，通过串口读取传感器数据并发布到实时话题。

## 功能特性

- 通过串口读取六维力传感器数据
- 支持1000Hz采样频率
- 发布geometry_msgs/WrenchStamped消息到实时话题
- 支持配置串口参数
- 线程安全的数据处理
- 高性能设计，确保实时性

## 消息格式

传感器数据按照以下格式解析：
- 第1字节：消息头 (0xA5)
- 第2-5字节：时间戳 (float, 小端序)
- 第6-9字节：通道1数据 (Fx, float, 小端序)
- 第10-13字节：通道2数据 (Fy, float, 小端序)
- 第14-17字节：通道3数据 (Fz, float, 小端序)
- 第18-21字节：通道4数据 (Mx, float, 小端序)
- 第22-25字节：通道5数据 (My, float, 小端序)
- 第26-29字节：通道6数据 (Mz, float, 小端序)
- 第30-33字节：温度数据 (float, 小端序)
- 第34-35字节：保留字段
- 第36字节：消息尾 (0x0D)

## 安装依赖

```bash
# 安装serial库
sudo apt-get install libserial-dev

# 或者使用pip安装
pip install pyserial
```

## 编译

```bash
# 在catkin工作空间中编译
cd ~/catkin_ws
catkin_make
```

## 使用方法

### 1. 启动节点

```bash
# 使用默认串口
roslaunch ft_sensor_node ft_sensor_node.launch

# 指定串口
roslaunch ft_sensor_node ft_sensor_node.launch ft_sensor_port:=/dev/ttyUSB1
```

### 2. 查看话题

```bash
# 查看发布的话题
rostopic list

# 查看话题数据
rostopic echo /ft_sensor
```

### 3. 配置参数

编辑 `config/ft_sensor_node.yaml` 文件来修改配置：

```yaml
ft_sensor_node:
  serial_port: "/dev/ttyUSB0"
  baud_rate: 921600
  data_bits: 8
  stop_bits: 1
  parity: 0
  sampling_frequency: 1000
  message_size: 36
  topic_name: "/ft_sensor"
  debug_mode: false
```

## 话题

### 发布的话题

- `/ft_sensor` (geometry_msgs/WrenchStamped)
  - 六维力传感器数据
  - 包含力和力矩信息

## 参数

| 参数名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| serial_port | string | "/dev/ttyUSB0" | 串口设备路径 |
| baud_rate | int | 921600 | 波特率 |
| data_bits | int | 8 | 数据位 |
| stop_bits | int | 1 | 停止位 |
| parity | int | 0 | 校验位 (0=无校验) |
| sampling_frequency | int | 1000 | 采样频率 (Hz) |
| message_size | int | 36 | 消息大小 (字节) |
| topic_name | string | "/ft_sensor" | 发布话题名称 |
| debug_mode | bool | false | 调试模式 |

## 故障排除

### 串口权限问题

```bash
# 添加用户到dialout组
sudo usermod -a -G dialout $USER

# 重新登录或重启系统
```

### 串口设备不存在

```bash
# 查看可用串口设备
ls /dev/ttyUSB*

# 或者查看所有串口
ls /dev/tty*
```

### 编译错误

确保已安装所有依赖：

```bash
sudo apt-get install ros-<distro>-serial
```

## 性能优化

- 使用实时线程确保1000Hz采样精度
- 内存池管理避免频繁内存分配
- 零拷贝数据传输
- 线程安全的数据处理

## 许可证

MIT License 