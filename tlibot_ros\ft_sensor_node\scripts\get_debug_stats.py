#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import rospy
from ft_sensor_node.srv import GetDebugStats, GetDebugStatsRequest

def get_debug_stats():
    """获取FT传感器调试统计信息"""
    rospy.wait_for_service('get_debug_stats')
    try:
        get_stats = rospy.ServiceProxy('get_debug_stats', GetDebugStats)
        response = get_stats(GetDebugStatsRequest())
        
        print("=== FT Sensor Debug Stats ===")
        print(f"Total packets: {response.total_packets_received}")
        print(f"Valid packets: {response.valid_packets}")
        print(f"Invalid packets: {response.invalid_packets}")
        print(f"Success rate: {response.success_rate:.2f}%")
        print(f"Serial errors: {response.serial_errors}")
        print(f"Publish errors: {response.publish_errors}")
        print(f"Last packet time: {response.last_packet_time:.6f}")
        print("================================")
        
    except rospy.ServiceException as e:
        print(f"Service call failed: {e}")

if __name__ == "__main__":
    rospy.init_node('debug_stats_client')
    get_debug_stats() 