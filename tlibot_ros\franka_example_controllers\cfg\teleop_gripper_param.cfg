#!/usr/bin/env python
PACKAGE = "franka_example_controllers"

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

gripper_param = gen.add_group("GripperParameter", type="apply")

gripper_param.add("grasp_force", double_t, 0,
                  "Grasping force to be applied on an object. [N]", 40.0, 1.0,
                  60.0)
gripper_param.add("move_speed", double_t, 0,
                  "Speed of the follower gripper when opening [m/s]", 0.3,
                  0.01, 0.4)

exit(gen.generate(PACKAGE, "teleop_gripper_param", "teleop_gripper_param"))
