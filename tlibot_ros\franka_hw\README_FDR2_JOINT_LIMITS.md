# FDR2 Joint Limits Interface

本文档说明了为FDR2机械臂实现的joint_limits_interface功能。

## 概述

FDR2机械臂的joint_limits_interface实现提供了以下功能：

1. **自动机械臂类型检测**：根据arm_id或关节名称自动检测是否为FDR2机械臂
2. **专用关节限制**：为FDR2机械臂定义了专门的加速度和急动度限制
3. **软限制支持**：在URDF文件中添加了safety_controller配置
4. **兼容性**：保持与原有Franka机械臂的兼容性

## 文件修改

### 1. 新增文件

- `include/franka_hw/fdr2_limits.h`：定义FDR2机械臂的关节限制常量
- `test/test_fdr2_joint_limits.cpp`：GTest测试文件
- `test_fdr2_simple.cpp`：简单测试文件（不依赖GTest）

### 2. 修改文件

- `include/franka_hw/franka_hw.h`：
  - 添加了`#include <franka_hw/fdr2_limits.h>`
  - 添加了`isFdr2Robot()`方法声明
  - 修改了`setupLimitInterface`模板函数以支持FDR2

- `src/franka_hw.cpp`：
  - 实现了`isFdr2Robot()`方法
  - 在`setupLimitInterface`中添加了机械臂类型检测逻辑

- `tlibot_ros/franka_description/robots/fdr2/fdr2.urdf`：
  - 为所有7个关节添加了`safety_controller`配置

## FDR2关节限制参数

### 加速度限制 (rad/s²)
```cpp
constexpr std::array<double, 7> kMaxJointAcceleration = {
    15.0,  // joint1
    7.5,   // joint2
    10.0,  // joint3
    12.5,  // joint4
    15.0,  // joint5
    20.0,  // joint6
    20.0   // joint7
};
```

### 急动度限制 (rad/s³)
```cpp
constexpr std::array<double, 7> kMaxJointJerk = {
    7500.0,  // joint1
    3750.0,  // joint2
    5000.0,  // joint3
    6250.0,  // joint4
    7500.0,  // joint5
    10000.0, // joint6
    10000.0  // joint7
};
```

## 机械臂类型检测

系统通过以下方式自动检测FDR2机械臂：

1. **arm_id检测**：如果arm_id包含"fdr2"或"FDR2"字符串
2. **关节名称检测**：如果所有关节名称匹配"joint1", "joint2", ..., "joint7"模式

## URDF配置

每个关节都添加了以下软限制配置：

```xml
<safety_controller
  soft_lower_limit="-3.0"
  soft_upper_limit="3.0"
  k_position="100"
  k_velocity="40" />
```

## 使用方法

### 1. 配置参数

在启动文件中设置正确的参数：

```yaml
joint_names: ["joint1", "joint2", "joint3", "joint4", "joint5", "joint6", "joint7"]
arm_id: "fdr2_arm"
robot_description: # 加载fdr2.urdf文件
```

### 2. 编译

```bash
catkin_make
```

### 3. 运行测试

```bash
# 运行简单测试
cd tlibot_ros/franka_hw
./test_fdr2_simple

# 或者运行GTest测试（如果有GTest环境）
rostest franka_hw test_fdr2_joint_limits.test
```

## 接口功能

### Position Joint Limits
- 限制关节位置命令在安全范围内
- 应用软限制以避免硬限制碰撞

### Velocity Joint Limits  
- 限制关节速度命令
- 考虑加速度限制进行速度规划

### Effort Joint Limits
- 限制关节力矩命令
- 防止过大的力矩输出

## 兼容性

- 保持与原有Franka Panda机械臂的完全兼容性
- 自动检测机械臂类型，无需手动配置
- 现有的控制器和应用程序无需修改

## 注意事项

1. 确保URDF文件中包含正确的safety_controller配置
2. 关节限制参数是保守估计值，可根据实际需要调整
3. 在实际部署前请充分测试所有功能
4. 建议在仿真环境中先验证配置的正确性
